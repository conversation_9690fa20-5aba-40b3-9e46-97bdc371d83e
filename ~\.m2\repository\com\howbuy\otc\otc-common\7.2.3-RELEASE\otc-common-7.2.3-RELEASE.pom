<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.otc</groupId>
    <artifactId>otc-common</artifactId>
    <version>7.2.3-RELEASE</version>
    <packaging>pom</packaging>
    <name>otc-common</name>
    <description>otc common component</description>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <lombok.version>1.18.20</lombok.version>
        <project.reporting.outputEncoding>utf-8	</project.reporting.outputEncoding>
        <spring.version>3.2.9.RELEASE</spring.version>
        <baomidou.mybatis-plus.version>3.4.2</baomidou.mybatis-plus.version>
        <pagehelper.version>5.1.10</pagehelper.version>
        <httpclient.version>4.5.5</httpclient.version>
        <dubbo.version>2.6.5</dubbo.version>
        <jackson.version>2.11.4</jackson.version>
        <com.howbuy.howbuy-message-service.version>2.3.0-RELEASE</com.howbuy.howbuy-message-service.version>
        <com.howbuy.howbuy-message-amq.version>2.3.0-RELEASE</com.howbuy.howbuy-message-amq.version>
        <com.howbuy.howbuy-message-rocket.version>2.3.0-RELEASE</com.howbuy.howbuy-message-rocket.version>
        <com.howbuy.howbuy-cachemanagement.version>3.6.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.howbuy-boot-actuator.version>1.1.6-RELEASE</com.howbuy.howbuy-boot-actuator.version>
        <com.howbuy.howbuy-sharding-id.version>2.0.4-RELEASE</com.howbuy.howbuy-sharding-id.version>
        <com.howbuy.otc-common.version>7.2.3-RELEASE</com.howbuy.otc-common.version>
        <com.howbuy.howbuy-dfile.version>1.11.0-RELEASE</com.howbuy.howbuy-dfile.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.3.12.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.30</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
            <version>2.13.3</version>
        </dependency>
        
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.4</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
    </dependencies>
    <distributionManagement>
        <repository>
            <id>howbuy-releases</id>
            <name>howbuy-releases</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshots</id>
            <name>howbuy-snapshots</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>