package com.howbuy.tms.high.orders.dao.mapper.customize;

import com.github.pagehelper.Page;
import com.howbuy.tms.high.orders.dao.mapper.DealOrderPoAutoMapper;
import com.howbuy.tms.high.orders.dao.po.DealOrderPo;
import com.howbuy.tms.high.orders.dao.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 去O
 */
public interface DealOrderPoMapper extends DealOrderPoAutoMapper {

    /**
     * selectFundCancelOrder:(查询公墓撤单数据)
     *
     * @param txAcctNo
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2017年4月7日 下午6:29:50
     */
    CancelOrderVo selectFundCancelOrder(@Param("txAcctNo") String txAcctNo, @Param("dealNo") String dealNo);

    /**
     * 查询高端撤单子订单列表
     *
     * @param txAcctNo
     * @param mainDealNo
     * @return java.util.List<com.howbuy.tms.high.orders.dao.vo.CancelOrderVo>
     * @author: huaqiang.liu
     * @date: 2021/3/10 16:11
     * @since JDK 1.8
     */
    List<CancelOrderVo> selectFundCancelOrderSubOrders(@Param("txAcctNo") String txAcctNo, @Param("mainDealNo") String mainDealNo);

    /**
     * updateCancelOrder:更新撤单数据
     *
     * @param record
     * @return
     * <AUTHOR>
     * @date 2016-10-10 下午11:33:29
     */
    int updateCancelOrder(DealOrderPo record);

    /**
     * queryDealOrderListWithDirect:(根据条件查询高端订单列表: 包含直销交易记录)
     *
     * @param condition
     * @return
     * <AUTHOR>
     * @date 2017年4月17日 上午10:54:10
     */
    Page<DealOrderVo> queryDealOrderListWithDirect(@Param("condition") QueryDealOrderListCondition condition);

    /**
     * 查询香港订单数量
     */
    int countHkDealNum(@Param("condition") QueryDealOrderListCondition condition);

    /**
     * 查询好臻订单数量
     */
    int countHzDealNum(@Param("condition") QueryDealOrderListCondition condition);

    /**
     * selectNotCompleteBuyCount:(查询未完成的认申购记录数)
     *
     * @param txAcctNo
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2017年4月5日 上午11:31:24
     */
    int selectNotCompleteBuyCount(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode);

    List<String> selectNotCompleteBuyCountMap(@Param("txAcctNo") String txAcctNo, @Param("fundCodes") List<String> fundCodes);

    /**
     * selectCancelOrderList:(查询高端可撤订单)
     *
     * @param currDate 当前日期
     * @param txAcctNo 交易账号
     * @return
     * <AUTHOR>
     * @date 2017年4月25日 下午1:54:25
     */
    List<DealOrderVo> selectCancelOrderList(@Param("currDate") Date currDate, @Param("txAcctNo") String txAcctNo);

    /**
     * selectForceCancelOrderList:(查询高端可强撤订单)
     *
     * @param taTradeDt
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2017年7月11日 下午2:56:26
     */
    List<DealOrderVo> selectForceCancelOrderList(@Param("taTradeDt") String taTradeDt, @Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode);

    /**
     * 根据主订单号列表查询合并上报单汇总信息
     *
     * @param mainDealNoList
     * @return java.util.List<com.howbuy.tms.high.orders.dao.vo.DealOrderVo>
     * @author: huaqiang.liu
     * @date: 2021/3/15 15:10
     * @since JDK 1.8
     */
    List<DealOrderVo> selectMergeOrderTotalInfoList(@Param("mainDealNoList") List<String> mainDealNoList);

    /**
     * selectHisCpAcctNo:(查询历史购买资金账号)
     *
     * @param txAcctNo
     * @param disCode
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2017年7月5日 下午3:40:30
     */
    List<String> selectHisCpAcctNo(@Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode, @Param("fundCode") String fundCode, @Param("taCode") String taCode);

    /**
     * selectrOnWayModifyDivOrderList:(查询在途修改分红方式订单)
     *
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2017年7月12日 下午7:45:17
     */
    List<DealOrderPo> selectrOnWayModifyDivOrderList(@Param("txAcctNo") String txAcctNo);

    /**
     * selectAckModifyDivOrderListByAckDt:(查询指定日期确认的修改分红方式订单)
     *
     * @param txAcctNo
     * @param ackDt
     * @return java.util.List<com.howbuy.tms.high.orders.dao.vo.QueryModifyDivOrdelVo>
     * @author: huaqiang.liu
     * @date: 2020/10/19 11:13
     * @since JDK 1.8
     */
    List<QueryModifyDivOrdelVo> selectAckModifyDivOrderListByAckDt(@Param("txAcctNo") String txAcctNo, @Param("ackDt") String ackDt);

    /**
     * selectFirstBuyOrderCount:(查询客户首单数量)
     *
     * @param txAcctNo
     * @param fundCode
     * @return
     * <AUTHOR>
     * @date 2017年7月18日 下午4:58:12
     */
    int selectFirstBuyOrderCount(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode);

    /**
     * selectOnWayTradeNum:(查询在途交易笔数)
     *
     * @param disCodeList
     * @param txAcctNo
     * @return
     * <AUTHOR>
     * @date 2017年8月18日 下午3:55:18
     */
    int selectOnWayTradeNum(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo,@Param("notFilterHkFund")String notFilterHkFund, @Param("notFilterHzFund")String notFilterHzFund);

    /**
     * queryDealOrderListWithDirect:(根据条件查询高端强制取消，付款失败的递延订单)
     *
     * @param condition
     * @return
     * <AUTHOR>
     * @date 2017年4月17日 上午10:54:10
     */
    List<DealOrderVo> queryAdvanceDealOrderList(@Param("condition") QueryDealOrderListCondition condition);

    /**
     * queryDealOrderByDealNo:(根据订单号查询订单信息)
     *
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2017年12月19日 上午9:53:40
     */
    QueryDealOrderVo queryDealOrderByDealNo(@Param("dealNo") String dealNo);

    /**
     * getDealOrderMsgByDealNo:(根据订单号获取订单消息信息)
     *
     * @param dealNo
     * @return
     * <AUTHOR>
     * @date 2017年12月20日 下午2:43:42
     */
    QueryDealOrderMsgVo selectDealOrderMsgByDealNo(@Param("dealNo") String dealNo);

    /**
     * @param disCodeList
     * @param txAcctNo
     * @return java.lang.Integer
     * @Description 查询赎回在途笔数
     * <AUTHOR>
     * @Date 2019/8/15 10:17
     **/
    Integer selectRedeemOnWayTradeNum(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode);

    /**
     * 查询赎回在途订单数量
     */
    Integer selectRedeemOnWayTradeAuthNum(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("notFilterHkFund") String notFilterHkFund,@Param("notFilterHzFund") String notFilterHzFund);

    /**
     * 查询在途交易的资金账号
     *
     * @param txAcctNo
     * @param fundCode
     * @return java.util.List<java.lang.String>
     * @author: huaqiang.liu
     * @date: 2020/12/10 16:09
     * @since JDK 1.8
     */
    List<String> selectBuyOnwayCpAcctNo(@Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode);

    /**
     * @param condition
     * @return java.util.List<com.howbuy.tms.high.orders.dao.vo.QueryDealOrderRefundVo>
     * @description:柜台查询修改回款方向
     * @author: chuanguang.tang
     * @date: 2021/8/12 17:49
     * @since JDK 1.8
     */
    Page<QueryDealOrderRefundVo> queryDealOrderForRefund(@Param("condition") QueryDealOrderRefundCondition condition);

    /**
     * selectUnpaid:(查询待付款订单：取申请成功但未付款的购买订单笔数)
     *
     * @param disCodeList
     * @param txAcctNo
     * @return
     * <AUTHOR> @date
     */
    List<DealOrderVo> selectUnpaid(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("notFilterHkFund")String notFilterHkFund, @Param("notFilterHzFund")String notFilterHzFund,@Param("onlyHkProduct") String onlyHkProduct);

    /**
     * selectUnconfirmed:(查询待确认订单：购买订单-取付款成功但未确认的订单笔数、赎回订单-取申请成功但未确认的赎回订单笔数)
     *
     * @param disCodeList
     * @param txAcctNo
     * @return
     * <AUTHOR> @date
     */
    List<DealOrderVo> selectUnconfirmed(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("notFilterHkFund")String notFilterHkFund, @Param("notFilterHzFund")String notFilterHzFund,@Param("onlyHkProduct") String onlyHkProduct);

    /**
     * 查询直销-赎回待回款订单数
     */
    List<RefundDealOrderVo> selectDirectRedeemReturn(@Param("txAcctNo") String txAcctNo, @Param("tradedt") String tradedt, @Param("dataQueryType")String notFilterHkFund);

    /**
     * 查询代销-购买待退款订单数
     *
     * @param txAcctNo
     * @param refundDt
     * @param payOutDt
     * @param ackDt
     * @return
     */
    List<RefundDealOrderVo> selectConsignmentBuyRefund(@Param("txAcctNo") String txAcctNo, @Param("refundDt") String refundDt,
                                                 @Param("payOutDt") String payOutDt, @Param("ackDt") String ackDt,
                                                 @Param("dataQueryType")String dataQueryType);

    /**
     * 查询代销-赎回待回款订单数-未出款
     */
    List<RefundDealOrderVo> selectConsignmentRedeemReturnNotPay(@Param("txAcctNo") String txAcctNo, @Param("ackDt") String ackDt,
                                                          @Param("dataQueryType")String dataQueryType);

    /**
     * 查询代销-赎回待回款订单数-已出款
     *
     * @param txAcctNo
     * @param payOutDt
     * @param ackDt
     * @return
     */
    List<RefundDealOrderVo> selectConsignmentRedeemReturnPay(@Param("txAcctNo") String txAcctNo,
                                                       @Param("payOutDt") String payOutDt, @Param("ackDt") String ackDt,
                                                       @Param("dataQueryType")String dataQueryType);


    List<String> getNotCompleteBuyByTxAcctNoAndDisCode(@Param("txAcctNo") String txAcctNo, @Param("disCode") String disCode);

    DealOrderPo getByExternalDealNo(@Param("externalDealNo") String externalDealNo, @Param("disCode") String disCode);

    List<DealOrderPo> queryByDealNoList(@Param("dealNoList")List<String> dealNoList);

    int countUnPayOrPayingOrder(@Param("txAcctNo")String txAcctNo, @Param("disCode")String disCode, @Param("cpAcctNos")List<String> cpAcctNos, @Param("taTradeDt")String taTradeDt);

    int isUnCheckDealOrder(@Param("txAcctNo")String txAcctNo, @Param("disCode")String disCode, @Param("cpAcctNos")List<String> cpAcctNos);

    DealOrderPo selectByDealNo(@Param("dealNo")String dealNo);

    int updateByDealNoSelective(DealOrderPo dealOrderPo);
}