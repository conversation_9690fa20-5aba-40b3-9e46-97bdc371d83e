<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">  
  <modelVersion>4.0.0</modelVersion>  
  <groupId>com.howbuy.es</groupId>  
  <artifactId>es-web</artifactId>  
  <version>20241014-RELEASE</version>
  <packaging>pom</packaging>  
  <name>es-web</name>
  <properties> 
      
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>  
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>  
      
    <spring.version>3.2.12.RELEASE</spring.version>  
    <aspectj.version>1.6.8</aspectj.version>  
    <ftxconsole.version>4.15.0t-SNAPSHOT</ftxconsole.version>  
    <sitemesh.version>2.4.2</sitemesh.version>  
    <scheduler.version>0.0.3-SNAPSHOT</scheduler.version>  
    <druid.version>0.2.9</druid.version>  
    <cglib.version>2.2.0</cglib.version>  
    <mybatis.version>3.2.8</mybatis.version>  
    <mysql.version>5.1.14</mysql.version>  
    <activemq.version>5.11.2</activemq.version>
    <xson.version>1.0.1</xson.version>  
    <fastjson.version>1.1.41</fastjson.version>  
    <dubbo.version>2.5.3</dubbo.version>  
    <ojdbc6.version>11.2.0.2.0</ojdbc6.version>  
    <cache.version>3.0.1-RELEASE</cache.version>
    <zkclient.version>0.4</zkclient.version>  
    <zookeeper.version>3.4.6</zookeeper.version>  
    <hessian.version>4.0.7</hessian.version>  
    <pagehelper.version>3.6.3</pagehelper.version>  
    <atomikos.version>3.9.3</atomikos.version>  
    <javax.servlet.version>3.1.0</javax.servlet.version>  
    <jstl.version>1.2</jstl.version>  
    <taglibs.version>1.1.2</taglibs.version>  
    <javax.servlet.jsp.version>2.0</javax.servlet.jsp.version>  
    <javax.el.version>2.2</javax.el.version>  
    <org.mybatis.version>1.2.2</org.mybatis.version>  
    <org.apache.empire-db.version>2.4.2</org.apache.empire-db.version>  
    <javax.validation.version>1.1.0.Final</javax.validation.version>  
    <org.hibernate.version>4.1.0.Final</org.hibernate.version>  
    <ch.qos.logback.version>1.1.11</ch.qos.logback.version>
    <org.slf4j.version>1.7.22</org.slf4j.version>
    <commons.pool.version>1.6</commons.pool.version>  
    <com.howbuy.notifyUtil.version>1.0.0</com.howbuy.notifyUtil.version>  
    <com.howbuy.fps.version>RELEASE</com.howbuy.fps.version>
    <junit.version>4.9</junit.version>  
    <org.springframework.security.version>3.2.6.RELEASE</org.springframework.security.version>  
    <com.howbuy.fds.redis.version>RELEASE</com.howbuy.fds.redis.version>
    <com.howbuy.common.version>RELEASE</com.howbuy.common.version>
    <commons-fileupload.version>1.2</commons-fileupload.version>  
    <org.codehaus.jackson.version>1.9.13</org.codehaus.jackson.version>  
    <org.springframework.boot.version>1.2.0.RELEASE</org.springframework.boot.version>  
    <commonshttpclient.version>3.0</commonshttpclient.version>  
    <jxl.version>2.4.2</jxl.version>  
    <ftx.common.version>RELEASE</ftx.common.version>
    <ftxonline.version>RELEASE</ftxonline.version>
    <com.howbuy.ftx-common.version>RELEASE</com.howbuy.ftx-common.version>
    <lombok.version>1.14.4</lombok.version> 
    <commons.lang.version>2.4</commons.lang.version>
  <com.howbuy.es-gateway.version>3.4.1-RELEASE</com.howbuy.es-gateway.version>
<com.howbuy.es-web.version>20241014-RELEASE</com.howbuy.es-web.version>
<com.howbuy.es-common.version>3.4.1-RELEASE</com.howbuy.es-common.version>
<com.howbuy.common-config.version>3.5.7-RELEASE</com.howbuy.common-config.version>
<com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
<com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
<com.howbuy.ftx-common-model.version>2.0.8-RELEASE</com.howbuy.ftx-common-model.version>
<com.howbuy.ftx-common-utils.version>2.0.8-RELEASE</com.howbuy.ftx-common-utils.version>
<com.howbuy.es-gateway-facade.version>3.4.1-RELEASE</com.howbuy.es-gateway-facade.version>
</properties>
  <dependencyManagement> 
    <dependencies> 
      <dependency>
        <groupId>com.howbuy.fps</groupId>  
        <artifactId>fps-gateway-interface</artifactId>  
        <version>1.0.0-SNAPSHOT</version> 
      </dependency>
      <dependency> 
        <groupId>junit</groupId>  
        <artifactId>junit</artifactId>  
        <version>${junit.version}</version> 
      </dependency>  
      <dependency>
        <groupId>org.springframework</groupId>  
        <artifactId>spring-test</artifactId>  
        <version>${spring.version}</version> 
      </dependency>  
        
      <dependency> 
        <groupId>javax.servlet</groupId>  
        <artifactId>javax.servlet-api</artifactId>  
        <version>${javax.servlet.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>jstl</groupId>  
        <artifactId>jstl</artifactId>  
        <version>${jstl.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>taglibs</groupId>  
        <artifactId>standard</artifactId>  
        <version>${taglibs.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>javax.servlet.jsp</groupId>  
        <artifactId>jsp-api</artifactId>  
        <version>${javax.servlet.jsp.version}</version>  
        <scope>provided</scope> 
      </dependency>  
      <dependency> 
        <groupId>javax.el</groupId>  
        <artifactId>el-api</artifactId>  
        <version>${javax.el.version}</version>  
        <scope>provided</scope> 
      </dependency>  
        
      <dependency>
        <groupId>org.springframework</groupId>  
        <artifactId>spring-context</artifactId>  
        <version>${spring.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.springframework</groupId>  
        <artifactId>spring-webmvc</artifactId>  
        <version>${spring.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.springframework</groupId>  
        <artifactId>spring-context-support</artifactId>  
        <version>${spring.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.springframework</groupId>  
        <artifactId>spring-tx</artifactId>  
        <version>${spring.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.springframework</groupId>  
        <artifactId>spring-jdbc</artifactId>  
        <version>${spring.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.springframework</groupId>  
        <artifactId>spring-jms</artifactId>  
        <version>${spring.version}</version> 
      </dependency>  
      <dependency>
        <groupId>net.sourceforge.cglib</groupId>  
        <artifactId>com.springsource.net.sf.cglib</artifactId>  
        <version>${cglib.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.aspectj</groupId>  
        <artifactId>aspectjweaver</artifactId>  
        <version>${aspectj.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.aspectj</groupId>  
        <artifactId>aspectjrt</artifactId>  
        <version>${aspectj.version}</version> 
      </dependency>  
      
      <dependency>
		<groupId>com.howbuy.es</groupId>
		<artifactId>es-gateway-facade</artifactId>
		<version>${com.howbuy.es-gateway-facade.version}</version>
	  </dependency>  
      <dependency> 
        <groupId>com.howbuy.es</groupId>  
        <artifactId>es-web-common</artifactId>  
        <version>${com.howbuy.es-web.version}</version>
      </dependency>  
      <dependency> 
        <groupId>com.howbuy.es</groupId>  
        <artifactId>es-web-facade</artifactId>  
        <version>${com.howbuy.es-web.version}</version>
      </dependency>  
      <dependency> 
        <groupId>com.howbuy.es</groupId>  
        <artifactId>es-web-service</artifactId>  
        <version>${com.howbuy.es-web.version}</version>
      </dependency>  
      <dependency> 
        <groupId>com.howbuy.es</groupId>  
        <artifactId>es-web-dao</artifactId>  
        <version>${com.howbuy.es-web.version}</version>
      </dependency>  
      <dependency> 
        <groupId>com.howbuy.common</groupId>  
        <artifactId>common-service</artifactId>  
        <version>${com.howbuy.common-service.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>com.howbuy.common</groupId>  
        <artifactId>common-facade</artifactId>  
        <version>${com.howbuy.common-facade.version}</version> 
      </dependency>

      <dependency>
		<groupId>com.howbuy.es</groupId>
		<artifactId>es-common-utils</artifactId>
		<version>${com.howbuy.es-common.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.howbuy.pa.cache</groupId>
            <artifactId>howbuy-cache-client-trade</artifactId>
          </exclusion>
        </exclusions>
	  </dependency>
      
      <dependency> 
        <groupId>com.alibaba.druid</groupId>  
        <artifactId>druid-wrapper</artifactId>  
        <version>${druid.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.mybatis</groupId>  
        <artifactId>mybatis</artifactId>  
        <version>${mybatis.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>com.github.pagehelper</groupId>  
        <artifactId>pagehelper</artifactId>  
        <version>${pagehelper.version}</version> 
      </dependency>  
      <dependency>
        <groupId>com.caucho</groupId>  
        <artifactId>hessian</artifactId>  
        <version>${hessian.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.mybatis</groupId>  
        <artifactId>mybatis-spring</artifactId>  
        <version>${org.mybatis.version}</version> 
      </dependency>  
        
      <dependency> 
        <groupId>com.github.xsonorg</groupId>  
        <artifactId>xson-core</artifactId>  
        <version>${xson.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>com.alibaba</groupId>  
        <artifactId>fastjson</artifactId>  
        <version>${fastjson.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.apache.empire-db</groupId>  
        <artifactId>empire-db</artifactId>  
        <version>${org.apache.empire-db.version}</version>  
        <exclusions> 
          <exclusion> 
            <groupId>org.slf4j</groupId>  
            <artifactId>slf4j-log4j12</artifactId> 
          </exclusion> 
        </exclusions> 
      </dependency>  
      <dependency> 
        <groupId>opensymphony</groupId>  
        <artifactId>sitemesh</artifactId>  
        <version>${sitemesh.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>com.oracle</groupId>  
        <artifactId>ojdbc6</artifactId>  
        <version>${ojdbc6.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>com.alibaba</groupId>  
        <artifactId>dubbo</artifactId>  
        <version>${dubbo.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.apache.zookeeper</groupId>  
        <artifactId>zookeeper</artifactId>  
        <version>${zookeeper.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>com.101tec</groupId>  
        <artifactId>zkclient</artifactId>  
        <version>${zkclient.version}</version>  
        <exclusions> 
          <exclusion> 
            <groupId>org.apache.zookeeper</groupId>  
            <artifactId>zookeeper</artifactId> 
          </exclusion> 
        </exclusions> 
      </dependency>  
      <dependency> 
        <groupId>javax.validation</groupId>  
        <artifactId>validation-api</artifactId>  
        <version>${javax.validation.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.hibernate</groupId>  
        <artifactId>hibernate-validator</artifactId>  
        <version>${org.hibernate.version}</version>  
        <exclusions> 
          <exclusion> 
            <groupId>org.slf4j</groupId>  
            <artifactId>slf4j-api</artifactId> 
          </exclusion> 
        </exclusions> 
      </dependency>  
      <dependency>
        <groupId>commons-pool</groupId>  
        <artifactId>commons-pool</artifactId>  
        <version>${commons.pool.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.springframework.security</groupId>  
        <artifactId>spring-security-core</artifactId>  
        <version>${org.springframework.security.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>commons-fileupload</groupId>  
        <artifactId>commons-fileupload</artifactId>  
        <version>${commons-fileupload.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>org.codehaus.jackson</groupId>  
        <artifactId>jackson-mapper-asl</artifactId>  
        <version>${org.codehaus.jackson.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>commons-httpclient</groupId>  
        <artifactId>commons-httpclient</artifactId>  
        <version>${commonshttpclient.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>jexcelapi</groupId>  
        <artifactId>jxl</artifactId>  
        <version>${jxl.version}</version> 
      </dependency>  
      <dependency> 
        <groupId>com.howbuy</groupId>  
        <artifactId>ftx-common-model</artifactId>  
        <version>${com.howbuy.ftx-common-model.version}</version>
      </dependency>  
      <dependency> 
        <groupId>com.howbuy</groupId>  
        <artifactId>ftx-common-utils</artifactId>  
        <version>${com.howbuy.ftx-common-utils.version}</version>
      </dependency>  
      <dependency> 
        <groupId>org.projectlombok</groupId>  
        <artifactId>lombok</artifactId>  
        <version>${lombok.version}</version>  
        <scope>provided</scope> 
      </dependency>
      <dependency>
		<groupId>commons-lang</groupId>
		<artifactId>commons-lang</artifactId>
		<version>${commons.lang.version}</version>
	  </dependency>
      <dependency>
        <groupId>com.howbuy</groupId>
        <artifactId>howbuy-ccms-watcher</artifactId>
        <version>6.0.1-RELEASE</version>
        <exclusions>
          <exclusion>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.howbuy</groupId>
        <artifactId>howbuy-message-service</artifactId>
        <version>2.2.1-RELEASE</version>
        <exclusions>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.howbuy</groupId>
        <artifactId>howbuy-message-amq</artifactId>
        <version>2.2.1-RELEASE</version>
        <exclusions>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>activemq-all</artifactId>
            <groupId>org.apache.activemq</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-client</artifactId>
        <version>${activemq.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-broker</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-pool</artifactId>
        <version>${activemq.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-spring</artifactId>
        <version>${activemq.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-kahadb-store</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.fusesource.hawtbuf</groupId>
        <artifactId>hawtbuf</artifactId>
        <version>1.11</version>
      </dependency>
      <dependency>
        <groupId>com.howbuy</groupId>
        <artifactId>howbuy-message-rocket</artifactId>
        <version>2.2.1-RELEASE</version>
        <exclusions>
          <exclusion>
            <artifactId>netty-all</artifactId>
            <groupId>io.netty</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${org.slf4j.version}</version>
      </dependency>
      <dependency>
        <artifactId>logback-core</artifactId>
        <groupId>ch.qos.logback</groupId>
        <version>${ch.qos.logback.version}</version>
      </dependency>
      <dependency>
        <artifactId>logback-classic</artifactId>
        <groupId>ch.qos.logback</groupId>
        <version>${ch.qos.logback.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.nacos</groupId>
        <artifactId>nacos-spring-context</artifactId>
        <version>1.1.1</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>
  </dependencyManagement>  
  <repositories> 
    <repository> 
      <id>maven2-repository.dev.java.net</id>  
      <name>Java.net Repository for Maven</name>  
      <url>http://download.java.net/maven/2/</url>  
      <layout>default</layout> 
    </repository>  
    <repository> 
      <id>ReleaseRepo</id>  
      <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/thirdparty</url> 
    </repository>  
    <repository> 
      <id>nexus</id>  
      <name>local private nexus</name>  
      <url>http://maven.oschina.net/content/groups/public/</url>  
      <releases />  
      <snapshots /> 
    </repository> 
  </repositories>  
  <pluginRepositories> 
    <pluginRepository> 
      <id>nexus</id>  
      <name>local private nexus</name>  
      <url>http://maven.oschina.net/content/groups/public/</url>  
      <releases />  
      <snapshots> 
        <enabled>false</enabled> 
      </snapshots> 
    </pluginRepository> 
  </pluginRepositories>  
  <distributionManagement> 
    <repository> 
      <id>nexus-release</id>  
      <name>Nexus Releases Repository</name>  
      <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url> 
    </repository>  
    <snapshotRepository> 
      <id>nexus-snapshots</id>  
      <name>Nexus Snapshots Repository</name>  
      <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url> 
    </snapshotRepository> 
  </distributionManagement>  
  <build> 
    <plugins> 
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-compiler-plugin</artifactId>  
        <version>2.3.2</version>  
        <configuration> 
          <source>${java.version}</source>  
          <target>${java.version}</target>  
          <encoding>UTF-8</encoding> 
        </configuration> 
      </plugin>  
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-surefire-plugin</artifactId>  
        <version>2.5</version>  
        <configuration> 
          <skipTests>true</skipTests> 
        </configuration> 
      </plugin>  
      <plugin> 
        <artifactId>maven-source-plugin</artifactId>  
        <version>2.1</version>  
        <configuration> 
          <attach>true</attach> 
        </configuration>  
        <executions> 
          <execution> 
            <phase>compile</phase>  
            <goals> 
              <goal>jar</goal> 
            </goals> 
          </execution> 
        </executions> 
      </plugin> 
    </plugins> 
  </build>  
  </project>