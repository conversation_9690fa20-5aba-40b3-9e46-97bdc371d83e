package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description:待回款订单明细
 * @Author: yun.lu
 * Date: 2025/8/21 18:28
 */
@Data
public class RefundDealOrderInfo implements Serializable {
    /**
     * 订单号
     */
    private String dealNo;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 中台业务代码
     */
    private String mBusiCode;
    /**
     * 待回款金额
     */
    private BigDecimal refundAmt;
    /**
     * 赎回去向,0-银行卡,1-储蓄罐
     */
    private String redeemDirection;

    /**
     * 是否是买入撤单带资金到账,1:是,0:不是
     */
    private String isBuyFund;

}
