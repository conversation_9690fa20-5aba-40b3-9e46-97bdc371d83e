<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.tms</groupId>
		<artifactId>elasticsearch-center</artifactId>
		<version>20250325-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	
	<artifactId>elasticsearch-center-client</artifactId>
	<packaging>jar</packaging>
	<name>elasticsearch-center-client</name>
	
	
	<dependencies>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-client</artifactId>
		</dependency>
	</dependencies>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>1.7</source>
					<target>1.7</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
	            <groupId>org.apache.maven.plugins</groupId>
	            <artifactId>maven-jar-plugin</artifactId>
	            <configuration>
	                <archive>
	                    <manifestEntries>
	                        <Package-Stamp>${parelease}</Package-Stamp>
	                    </manifestEntries>
	                </archive>
	            </configuration>
	        </plugin>
		</plugins>
	</build>
	
</project>