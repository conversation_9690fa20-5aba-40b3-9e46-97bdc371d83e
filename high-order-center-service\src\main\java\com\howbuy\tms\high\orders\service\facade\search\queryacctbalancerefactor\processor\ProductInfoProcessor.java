package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.QueryAcctBalanceRefactorRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 产品信息处理器
 * @author: hongdong.xie
 * @date: 2025/8/26 15:10
 * @since JDK 1.8
 */
@Slf4j
@Component
public class ProductInfoProcessor {

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    /**
     * @description: 批量查询产品基本信息
     * @param productCodeSet 产品代码集合
     * @return Map<String, Object> 产品信息Map，Key为产品代码，Value为产品信息
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    public Map<String, Object> batchQueryProductInfo(Set<String> productCodeSet) {
        if (CollectionUtils.isEmpty(productCodeSet)) {
            return new HashMap<>();
        }

        log.info("批量查询产品基本信息，产品数量：{}", productCodeSet.size());

        try {
            List<String> productCodeList = new ArrayList<>(productCodeSet);
            Map<String, HighProductDBInfoBean> productInfoMap = queryHighProductOuterService.getHighProductDBInfoMap(productCodeList);
            
            if (CollectionUtils.isEmpty(productInfoMap)) {
                log.warn("未查询到任何产品基本信息");
                return new HashMap<>();
            }

            // 转换为通用Map
            Map<String, Object> resultMap = new HashMap<>();
            productInfoMap.forEach((key, value) -> resultMap.put(key, value));

            log.info("成功查询到{}个产品的基本信息", resultMap.size());
            return resultMap;

        } catch (Exception e) {
            log.error("批量查询产品基本信息异常：{}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * @description: 根据请求条件过滤产品
     * @param productInfoMap 产品信息Map
     * @param request 请求对象
     * @return Map<String, Object> 过滤后的产品信息Map
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    public Map<String, Object> filterProductsByRequest(Map<String, Object> productInfoMap, QueryAcctBalanceRefactorRequest request) {
        if (CollectionUtils.isEmpty(productInfoMap)) {
            return productInfoMap;
        }

        Map<String, Object> filteredMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : productInfoMap.entrySet()) {
            String productCode = entry.getKey();
            HighProductDBInfoBean productInfo = (HighProductDBInfoBean) entry.getValue();

            // 1. 产品类型过滤
            if (StringUtils.hasText(request.getProductType()) && 
                !request.getProductType().equals(productInfo.getFundType())) {
                continue;
            }

            // 2. 产品子类型过滤
            if (StringUtils.hasText(request.getProductSubType()) && 
                !request.getProductSubType().equals(productInfo.getFundSubType())) {
                continue;
            }

            // 3. 香港产品过滤
            if (!isHkProductAllowed(productInfo, request)) {
                continue;
            }

            // 4. 好臻产品过滤
            if (!isHzProductAllowed(productInfo, request)) {
                continue;
            }

            filteredMap.put(productCode, productInfo);
        }

        log.info("产品过滤完成，原始数量：{}，过滤后数量：{}", productInfoMap.size(), filteredMap.size());
        return filteredMap;
    }

    /**
     * @description: 判断香港产品是否允许
     * @param productInfo 产品信息
     * @param request 请求对象
     * @return boolean 是否允许
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    private boolean isHkProductAllowed(HighProductDBInfoBean productInfo, QueryAcctBalanceRefactorRequest request) {
        // 如果不过滤香港产品，则允许
        if (YesOrNoEnum.YES.getCode().equals(request.getNotFilterHkFund())) {
            return true;
        }

        // 如果产品不是香港产品，则允许
        if (!YesOrNoEnum.YES.getCode().equals(productInfo.getHkSaleFlag())) {
            return true;
        }

        // 如果请求明确要查询香港产品，则允许
        if (YesOrNoEnum.YES.getCode().equals(request.getHkSaleFlag())) {
            return true;
        }

        // 其他情况不允许香港产品
        return false;
    }

    /**
     * @description: 判断好臻产品是否允许
     * @param productInfo 产品信息
     * @param request 请求对象
     * @return boolean 是否允许
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    private boolean isHzProductAllowed(HighProductDBInfoBean productInfo, QueryAcctBalanceRefactorRequest request) {
        // 如果不过滤好臻产品，则允许
        if (YesOrNoEnum.YES.getCode().equals(request.getNotFilterHzFund())) {
            return true;
        }

        // 如果分销机构列表包含好臻，则允许好臻产品
        if (!CollectionUtils.isEmpty(request.getDisCodeList()) && 
            request.getDisCodeList().contains(DisCodeEnum.HZ.getCode())) {
            return true;
        }

        // 如果产品不是好臻产品，则允许
        // 注意：这里需要根据实际业务逻辑判断产品是否为好臻产品
        // 可能需要通过产品代码前缀、分销机构等信息判断
        return !isHzProduct(productInfo);
    }

    /**
     * @description: 判断是否为好臻产品
     * @param productInfo 产品信息
     * @return boolean 是否为好臻产品
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    private boolean isHzProduct(HighProductDBInfoBean productInfo) {
        // 根据产品代码前缀判断（具体规则需要根据业务确定）
        if (productInfo.getFundCode() != null && productInfo.getFundCode().startsWith("HZ")) {
            return true;
        }

        // 根据其他业务规则判断
        // 例如：特定的产品类型、分销机构等
        
        return false;
    }

    /**
     * @description: 获取需要查询净值的产品代码集合
     * @param productInfoMap 产品信息Map
     * @return Set<String> 需要查询净值的产品代码集合
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    public Set<String> getNavQueryProductCodes(Map<String, Object> productInfoMap) {
        return productInfoMap.entrySet().stream()
                .filter(entry -> needQueryNav((HighProductDBInfoBean) entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    /**
     * @description: 获取需要查询收益的产品代码集合
     * @param productInfoMap 产品信息Map
     * @return Set<String> 需要查询收益的产品代码集合
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    public Set<String> getIncomeQueryProductCodes(Map<String, Object> productInfoMap) {
        return productInfoMap.entrySet().stream()
                .filter(entry -> needQueryIncome((HighProductDBInfoBean) entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    /**
     * @description: 获取需要查询回款的产品代码集合（股权产品）
     * @param productInfoMap 产品信息Map
     * @return Set<String> 需要查询回款的产品代码集合
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    public Set<String> getCashCollectionQueryProductCodes(Map<String, Object> productInfoMap) {
        return productInfoMap.entrySet().stream()
                .filter(entry -> needQueryCashCollection((HighProductDBInfoBean) entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    /**
     * @description: 判断是否需要查询净值
     * @param productInfo 产品信息
     * @return boolean 是否需要查询净值
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    private boolean needQueryNav(HighProductDBInfoBean productInfo) {
        // 股权产品不需要查询净值
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(productInfo.getFundSubType())) {
            return false;
        }
        
        // 其他产品需要查询净值
        return true;
    }

    /**
     * @description: 判断是否需要查询收益
     * @param productInfo 产品信息
     * @return boolean 是否需要查询收益
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    private boolean needQueryIncome(HighProductDBInfoBean productInfo) {
        // 股权产品不通过资产中心查询收益
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(productInfo.getFundSubType())) {
            return false;
        }
        
        // 其他产品需要查询收益
        return true;
    }

    /**
     * @description: 判断是否需要查询回款（股权产品）
     * @param productInfo 产品信息
     * @return boolean 是否需要查询回款
     * @author: hongdong.xie
     * @date: 2025/8/26 15:10
     * @since JDK 1.8
     */
    private boolean needQueryCashCollection(HighProductDBInfoBean productInfo) {
        // 只有股权产品需要查询回款
        return ProductDBTypeEnum.GUQUAN.getCode().equals(productInfo.getFundSubType());
    }
}
