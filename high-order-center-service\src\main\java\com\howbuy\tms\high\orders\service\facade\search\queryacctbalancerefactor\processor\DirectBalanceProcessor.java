package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor;

import com.howbuy.tms.common.enums.busi.ScaleTypeEnum;
import com.howbuy.tms.high.orders.dao.po.CmCustFundDirectPo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.BalanceInfoBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.context.BalanceQueryContext;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.converter.BalanceDataConverter;
import com.howbuy.tms.high.orders.service.repository.CmCustFundDirectRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 直销持仓处理器
 * @author: hongdong.xie
 * @date: 2025/8/26 15:30
 * @since JDK 1.8
 */
@Slf4j
@Component
public class DirectBalanceProcessor {

    @Autowired
    private CmCustFundDirectRepository cmCustFundDirectRepository;

    @Autowired
    private BalanceDataConverter balanceDataConverter;

    @Autowired
    private ProductInfoProcessor productInfoProcessor;

    @Autowired
    private MarketValueCalculator marketValueCalculator;

    /**
     * @description: 处理直销持仓
     * @param context 查询上下文
     * @return List<BalanceInfoBean> 直销持仓列表
     * @author: hongdong.xie
     * @date: 2025/8/26 15:30
     * @since JDK 1.8
     */
    public List<BalanceInfoBean> processDirectBalance(BalanceQueryContext context) {
        log.info("开始处理直销持仓，txAcctNo:{}, hbOneNo:{}", context.getTxAcctNo(), context.getHbOneNo());

        // 1. 查询直销持仓数据
        List<CmCustFundDirectPo> directPoList = queryDirectBalance(context);
        if (CollectionUtils.isEmpty(directPoList)) {
            log.info("未查询到直销持仓数据");
            return new ArrayList<>();
        }

        // 2. 获取产品代码集合
        Set<String> productCodeSet = directPoList.stream()
                .map(CmCustFundDirectPo::getFundcode)
                .collect(Collectors.toSet());

        // 3. 批量查询产品基本信息
        Map<String, Object> productInfoMap = productInfoProcessor.batchQueryProductInfo(productCodeSet);

        // 4. 根据请求条件过滤产品
        productInfoMap = productInfoProcessor.filterProductsByRequest(productInfoMap, context.getRequest());

        // 5. 转换为持仓信息Bean
        List<BalanceInfoBean> balanceList = convertToBalanceInfoBeans(directPoList, productInfoMap, context);

        // 6. 计算市值和收益
        for (BalanceInfoBean balanceBean : balanceList) {
            marketValueCalculator.calculateMarketValue(balanceBean, context);
        }

        log.info("直销持仓处理完成，共{}条记录", balanceList.size());
        return balanceList;
    }

    /**
     * @description: 查询直销持仓数据
     * @param context 查询上下文
     * @return List<CmCustFundDirectPo> 直销持仓数据列表
     * @author: hongdong.xie
     * @date: 2025/8/26 15:30
     * @since JDK 1.8
     */
    private List<CmCustFundDirectPo> queryDirectBalance(BalanceQueryContext context) {
        List<String> productCodeList = null;
        if (StringUtils.hasText(context.getProductCode())) {
            productCodeList = Collections.singletonList(context.getProductCode());
        }

        return cmCustFundDirectRepository.selectDirectBalance(
                context.getDisCodeList(),
                context.getTxAcctNo(),
                productCodeList,
                context.getBalanceStatus()
        );
    }

    /**
     * @description: 转换为持仓信息Bean列表
     * @param directPoList 直销持仓PO列表
     * @param productInfoMap 产品信息Map
     * @param context 查询上下文
     * @return List<BalanceInfoBean> 持仓信息Bean列表
     * @author: hongdong.xie
     * @date: 2025/8/26 15:30
     * @since JDK 1.8
     */
    private List<BalanceInfoBean> convertToBalanceInfoBeans(List<CmCustFundDirectPo> directPoList,
                                                            Map<String, Object> productInfoMap,
                                                            BalanceQueryContext context) {
        List<BalanceInfoBean> balanceList = new ArrayList<>();

        for (CmCustFundDirectPo directPo : directPoList) {
            Object productInfo = productInfoMap.get(directPo.getFundcode());
            if (productInfo == null) {
                log.warn("未找到产品信息，产品代码：{}", directPo.getFundcode());
                continue;
            }

            // 转换为BalanceInfoBean
            BalanceInfoBean balanceBean = balanceDataConverter.convertFromDirectPo(directPo, productInfo, context.getDisCodeList());
            balanceBean.setScaleType(ScaleTypeEnum.DIRECT.getCode());

            // 计算直销产品的净购买金额
            calculateDirectNetBuyAmount(balanceBean, directPo, context);

            balanceList.add(balanceBean);
        }

        return balanceList;
    }

    /**
     * @description: 计算直销产品的净购买金额
     * @param balanceBean 持仓信息Bean
     * @param directPo 直销持仓PO
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:30
     * @since JDK 1.8
     */
    private void calculateDirectNetBuyAmount(BalanceInfoBean balanceBean, CmCustFundDirectPo directPo, BalanceQueryContext context) {
        // 直销产品的净购买金额需要通过交易记录计算
        // 净购买金额 = 申购金额 - 赎回金额
        
        try {
            // TODO: 查询直销产品的交易记录，计算净购买金额
            // 这里需要实现具体的交易记录查询和计算逻辑
            
            // 临时使用持仓份额作为净购买金额（实际应该通过交易记录计算）
            if (directPo.getBalancevol() != null) {
                balanceBean.setNetBuyAmount(directPo.getBalancevol());
                balanceBean.setCurrencyNetBuyAmount(directPo.getBalancevol());
            }
            
        } catch (Exception e) {
            log.error("计算直销产品净购买金额异常，产品代码：{}，异常信息：{}", directPo.getFundcode(), e.getMessage(), e);
            // 异常情况下设置为0
            balanceBean.setNetBuyAmount(java.math.BigDecimal.ZERO);
            balanceBean.setCurrencyNetBuyAmount(java.math.BigDecimal.ZERO);
        }
    }

    /**
     * @description: 查询直销产品交易记录
     * @param productCode 产品代码
     * @param txAcctNo 交易账号
     * @param context 查询上下文
     * @return List<Object> 交易记录列表
     * @author: hongdong.xie
     * @date: 2025/8/26 15:30
     * @since JDK 1.8
     */
    private List<Object> queryDirectTransactionRecords(String productCode, String txAcctNo, BalanceQueryContext context) {
        // TODO: 实现直销产品交易记录查询
        // 查询 high_deal_order_dtl 表中的交易记录
        // 按产品代码和交易账号过滤
        // 计算申购金额和赎回金额
        return new ArrayList<>();
    }

    /**
     * @description: 计算净购买金额
     * @param transactionRecords 交易记录列表
     * @return java.math.BigDecimal 净购买金额
     * @author: hongdong.xie
     * @date: 2025/8/26 15:30
     * @since JDK 1.8
     */
    private java.math.BigDecimal calculateNetBuyAmountFromTransactions(List<Object> transactionRecords) {
        // TODO: 根据交易记录计算净购买金额
        // 申购金额 - 赎回金额 = 净购买金额
        return java.math.BigDecimal.ZERO;
    }
}
