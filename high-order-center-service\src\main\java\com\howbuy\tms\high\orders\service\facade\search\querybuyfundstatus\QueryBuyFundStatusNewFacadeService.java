/**
 * Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus;

import cn.hutool.core.thread.ExecutorBuilder;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.interlayer.common.enums.ShareClassEnum;
import com.howbuy.interlayer.product.model.HighProductCanBuyInfoModel;
import com.howbuy.tms.cache.service.highquota.HighQuotaBeanNew;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BuyStatusEnum;
import com.howbuy.tms.common.enums.busi.BuyStatusTypeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusNewFacade;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusRequest;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse;
import com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusResponse.BuyFundStatusBean;
import com.howbuy.tms.high.orders.service.business.fundbuystatus.FundBuyStatusService;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.FundBuyStatusDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusListParam;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import com.howbuy.tms.high.orders.service.service.highdealorderdtl.QueryHighDealOrderParam;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:(查询产品购买状态)
 */
@DubboService
@Service("queryBuyFundStatusNewFacade")
@RefreshScope
public class QueryBuyFundStatusNewFacadeService implements QueryBuyFundStatusNewFacade {
    private static final Logger logger = LogManager.getLogger(QueryBuyFundStatusNewFacadeService.class);

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private FundBuyStatusService fundBuyStatusService;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;

    @Value("${queryBuyFundStatus.homeCount:10}")
    private int homeCount;

    @Value("${queryBuyFundStatus.unHomeCount:30}")
    private int unHomeCount;

    @Value("${queryBuyFundStatus.maxCount:100}")
    private int maxCount;

    private static final String NO_TX_ACCT_NO = "noTxAcctNo";

    /**
     * 缓存服务
     */
    private static final CacheService CACHE_SERVICE = CacheServiceImpl.getInstance();


    /**
     * 构建批量查询的线程池
     */
    private final ExecutorService queryBuyFundStatusNewFacadeExecutor = ExecutorBuilder.create().setCorePoolSize(10)
            .setMaxPoolSize(64).setThreadFactory(new ThreadFactoryBuilder().setNameFormat("queryBuyFundStatusNewFacade-fund-pool-%d").build())
            .build();


    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.querybuyfundstatus.QueryBuyFundStatusNewFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryBuyFundStatusNewFacadeService
     * @apiName execute
     * @apiParam (请求参数) {Array} productCodeList 产品代码
     * @apiParam (请求参数) {String} appointmentFlag 预约日历场景标识 1-是
     * @apiParam (请求参数) {String} homePage 是否首页使用 1-是
     * @apiParam (请求参数) {String} notCheckChannel 是否不校验渠道
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=LvWx4vRpuk&notCheckChannel=lBiy7Bz&productCodeList=IDWfgsyY7W&pageSize=778&disCode=6pe&txChannel=QGEs6A&homePage=q5QVud1v9&appTm=n3le9c7VQ&subOutletCode=mws&pageNo=456&operIp=3&txAcctNo=9rW&appointmentFlag=ZxBJ6en&appDt=Qi&dataTrack=r5zDtE&txCode=5h8hrUA&outletCode=CIbeDBZYUg
     * @apiSuccess (响应结果) {String} txAcctNo 交易账号
     * @apiSuccess (响应结果) {Array} buyFundStatusList 产品状态list
     * @apiSuccess (响应结果) {String} buyFundStatusList.productCode 产品代码
     * @apiSuccess (响应结果) {String} buyFundStatusList.shareClass 收费类型A-前收费;B-后收费
     * @apiSuccess (响应结果) {String} buyFundStatusList.buyStatus 产品状态0-不可购买 1-可购买
     * @apiSuccess (响应结果) {String} buyFundStatusList.fundBuyStatus 产品购买状态,CAN_BUY:可购买,CAN_NOT_BUY:不可购买,CAN_MODIFY:可修改,CAN_NOT_MODIFY:不可修改;
     * @apiSuccess (响应结果) {String} buyFundStatusList.buyStatusType 状态标识1-正常 2-代销不支持 3.年龄限制 4-已售罄 5-直销转代销的黑名单6-产品状态不可购买或不在预约期内          8-产品参数配置有误99-其它
     * @apiSuccess (响应结果) {String} buyFundStatusList.msg 说明信息
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"HOPCII5","buyFundStatusList":[{"msg":"s","productCode":"jYDoMHD1","shareClass":"LulY2","buyStatus":"76GT0SJeq","buyStatusType":"R","fundBuyStatus":"qhxkxf7"}],"totalPage":3313,"pageNo":6154,"txAcctNo":"K","description":"Zjwkg4","totalCount":4949}
     */
    @Override
    public QueryBuyFundStatusResponse execute(QueryBuyFundStatusRequest request) {
        QueryBuyFundStatusResponse response = new QueryBuyFundStatusResponse();
        response.setTxAcctNo(request.getTxAcctNo());
        response.setReturnCode(ExceptionCodes.SUCCESS);
        List<String> productCodeList = request.getProductCodeList();
        // 0.参数校验
        checkParam(request, response);
        if (!ExceptionCodes.SUCCESS.equals(response.getReturnCode())) {
            return response;
        }
        long startTime = System.currentTimeMillis();
        long endTime = System.currentTimeMillis();

        // 1.获取交易日
        startTime = System.currentTimeMillis();
        String taTradeDt = queryTradeDayOuterService.getWorkDay(request.getAppDt(), request.getAppTm());
        endTime = System.currentTimeMillis();
        logger.info("获取交易日耗时:{}", endTime - startTime);

        // 2.查询客户信息
        startTime = System.currentTimeMillis();
        QueryCustInfoResult custInfo = null;
        if (StringUtils.isNotEmpty(request.getTxAcctNo())) {
            custInfo = fundBuyStatusService.queryCustInfo(request.getTxAcctNo(), request.getDisCode());
        }
        endTime = System.currentTimeMillis();
        logger.info("查询客户信息耗时:{}", endTime - startTime);

        // 3.获取直销黑名单
        startTime = System.currentTimeMillis();
        List<String> blackFundCodes = new ArrayList<>();
        String hboneNo = null;
        String txAcctNo = null;
        if (custInfo != null && StringUtils.isNotEmpty(custInfo.getHboneNo())) {
            hboneNo = custInfo.getHboneNo();
            txAcctNo = custInfo.getTxAcctNo();
            blackFundCodes = CACHE_SERVICE.get(CacheKeyPrefix.HIGH_CM_BLACK + custInfo.getHboneNo());
            if (blackFundCodes == null) {
                blackFundCodes = new ArrayList<>();
            }
        }
        endTime = System.currentTimeMillis();
        logger.info("获取直销黑名单耗时:{}", endTime - startTime);

        // 4.查询所有产品信息
        startTime = System.currentTimeMillis();
        String appDtm = request.getAppDt() + request.getAppTm();
        Map<String, HighProductCanBuyInfoModel> productInfoModelMap = queryHighProductOuterService.getHighProductAllInfoByCanBuy(request.getProductCodeList(), hboneNo, appDtm, request.getAppointmentFlag());
        endTime = System.currentTimeMillis();
        logger.info("查询所有产品信息耗时:{}", endTime - startTime);

        // 5.获取好臻产品相关信息
        startTime = System.currentTimeMillis();
        List<String> hzFundCodeList = new ArrayList<>();
        for (Map.Entry<String, HighProductCanBuyInfoModel> entry : productInfoModelMap.entrySet()) {
            HighProductCanBuyInfoModel highProductCanBuyInfoModel = entry.getValue();
            if (DisCodeEnum.HZ.getCode().equals(highProductCanBuyInfoModel.getControlDisCode())) {
                hzFundCodeList.add(highProductCanBuyInfoModel.getFundCode());
            }
        }
        Map<String, List<HighDealOrderDtlPo>> hzOnWayAgentDealDtlMap = getHzOnWayAgentDealDtlMap(txAcctNo, hzFundCodeList);
        Map<String, List<AcctBalanceBaseInfo>> hzConfirmBalanceBaseInfoMap = getHzConfirmBalanceBaseInfoMap(txAcctNo, hzFundCodeList);
        endTime = System.currentTimeMillis();
        logger.info("获取好臻产品相关信息耗时:{}", endTime - startTime);

        // 6.获取合伙人数
        startTime = System.currentTimeMillis();
        Integer partnersNumber = 1;
        if (StringUtils.isNotEmpty(txAcctNo)) {
            partnersNumber = queryHighProductOuterService.getHighPartnersNumber(custInfo.getTxAcctNo());
        }
        endTime = System.currentTimeMillis();
        logger.info("获取合伙人数耗时:{}", endTime - startTime);

        // 7.获取在途或者持仓产品列表 - 未走缓存
        startTime = System.currentTimeMillis();
        List<String> ccOrZtList = fundBuyStatusService.getCcOrZtList(request.getTxAcctNo(), productCodeList);
        endTime = System.currentTimeMillis();
        logger.info("获取在途或者持仓产品列表耗时:{}", endTime - startTime);

        // 8.查询所有产品的已使用额度
        startTime = System.currentTimeMillis();
        List<HighQuotaBeanNew> highQuotaBeans = CACHE_SERVICE.mget4SamePrefixKeys(getBatchProductTotalKeys(productCodeList));
        Map<String, HighQuotaBeanNew> highQuotaBeanNewMap = highQuotaBeans.stream().filter(Objects::nonNull).collect(Collectors.toMap(HighQuotaBeanNew::getFundCode, highQuotaBeanNew -> highQuotaBeanNew));
        endTime = System.currentTimeMillis();
        logger.info("查询所有产品的已使用额度耗时{}", endTime - startTime);

        // 9.分片处理
        int maxNum = unHomeCount;
        if (request.getHomePage() != null && YesOrNoEnum.YES.getCode().equals(request.getHomePage())) {
            maxNum = homeCount;
        }

        // 10.处理
        List<FundBuyStatusDto> fundBuyStatusDtos = getFundBuyStatusDtos(request, productCodeList, taTradeDt, custInfo, blackFundCodes, hboneNo, productInfoModelMap, hzOnWayAgentDealDtlMap, hzConfirmBalanceBaseInfoMap, partnersNumber, ccOrZtList, highQuotaBeanNewMap, maxNum);

        // 11.结果数据
        List<BuyFundStatusBean> list = new ArrayList<>();
        for (FundBuyStatusDto fundBuyStatusDto : fundBuyStatusDtos) {
            BuyFundStatusBean buyFundStatusBean = new BuyFundStatusBean();
            buyFundStatusBean.setMsg(fundBuyStatusDto.getMsg());
            buyFundStatusBean.setShareClass(fundBuyStatusDto.getShareClass());
            buyFundStatusBean.setProductCode(fundBuyStatusDto.getProductCode());
            buyFundStatusBean.setBuyStatus(fundBuyStatusDto.getFundBuyStatusEnum().getCanBuy());
            buyFundStatusBean.setFundBuyStatus(fundBuyStatusDto.getFundBuyStatusEnum().getStatus());
            list.add(buyFundStatusBean);
        }
        response.setBuyFundStatusList(list);
        return response;
    }

    private List<FundBuyStatusDto> getFundBuyStatusDtos(QueryBuyFundStatusRequest request, List<String> productCodeList, String taTradeDt, QueryCustInfoResult custInfo, List<String> blackFundCodes, String hboneNo, Map<String, HighProductCanBuyInfoModel> productInfoModelMap, Map<String, List<HighDealOrderDtlPo>> hzOnWayAgentDealDtlMap, Map<String, List<AcctBalanceBaseInfo>> hzConfirmBalanceBaseInfoMap, Integer partnersNumber, List<String> ccOrZtList, Map<String, HighQuotaBeanNew> highQuotaBeanNewMap, int maxNum) {
        List<FundBuyStatusDto> fundBuyStatusDtos;
        if (productCodeList.size() <= maxNum) {
            // 非线程池处理
            QueryFundBuyStatusListParam queryFundBuyStatusListParam = new QueryFundBuyStatusListParam();
            queryFundBuyStatusListParam.setFundCodeList(productCodeList);
            queryFundBuyStatusListParam.setDisCode(request.getDisCode());
            queryFundBuyStatusListParam.setTxAcctNo(request.getTxAcctNo());
            queryFundBuyStatusListParam.setAppDt(request.getAppDt());
            queryFundBuyStatusListParam.setAppTm(request.getAppTm());
            queryFundBuyStatusListParam.setAppointmentFlag(request.getAppointmentFlag());
            queryFundBuyStatusListParam.setNotCheckChannel(request.getNotCheckChannel());
            queryFundBuyStatusListParam.setTaTradeDt(taTradeDt);
            queryFundBuyStatusListParam.setTxChannel(request.getTxChannel());
            queryFundBuyStatusListParam.setProductInfoModelMap(productInfoModelMap);
            queryFundBuyStatusListParam.setCustInfo(custInfo);
            queryFundBuyStatusListParam.setCcOrZtList(ccOrZtList);
            queryFundBuyStatusListParam.setBlackFundCodes(blackFundCodes);
            queryFundBuyStatusListParam.setHighQuotaBeanNewMap(highQuotaBeanNewMap);
            queryFundBuyStatusListParam.setPartnersNumber(partnersNumber);
            queryFundBuyStatusListParam.setHzOnWayAgentDealDtlMap(hzOnWayAgentDealDtlMap);
            queryFundBuyStatusListParam.setHzConfirmBalanceBaseInfoMap(hzConfirmBalanceBaseInfoMap);
            fundBuyStatusDtos = fundBuyStatusService.queryProcess(queryFundBuyStatusListParam);
        } else {
            // 线程池处理
            long startTime = System.currentTimeMillis();
            List<List<String>> fundCodesList = Lists.partition(productCodeList, maxNum);
            List<CompletableFuture<List<FundBuyStatusDto>>> completableFutures = fundCodesList.stream().map(fundCodeList ->
                            CompletableFuture.supplyAsync(ThreadTraceHelper.decorateSupplier(() -> {
                                QueryFundBuyStatusListParam queryFundBuyStatusListParam = new QueryFundBuyStatusListParam();
                                queryFundBuyStatusListParam.setFundCodeList(fundCodeList);
                                queryFundBuyStatusListParam.setDisCode(request.getDisCode());
                                queryFundBuyStatusListParam.setTxAcctNo(request.getTxAcctNo());
                                queryFundBuyStatusListParam.setAppDt(request.getAppDt());
                                queryFundBuyStatusListParam.setAppTm(request.getAppTm());
                                queryFundBuyStatusListParam.setAppointmentFlag(request.getAppointmentFlag());
                                queryFundBuyStatusListParam.setNotCheckChannel(request.getNotCheckChannel());
                                queryFundBuyStatusListParam.setTaTradeDt(taTradeDt);
                                queryFundBuyStatusListParam.setTxChannel(request.getTxChannel());
                                queryFundBuyStatusListParam.setProductInfoModelMap(productInfoModelMap);
                                queryFundBuyStatusListParam.setCustInfo(custInfo);
                                queryFundBuyStatusListParam.setCcOrZtList(ccOrZtList);
                                queryFundBuyStatusListParam.setBlackFundCodes(blackFundCodes);
                                queryFundBuyStatusListParam.setHighQuotaBeanNewMap(highQuotaBeanNewMap);
                                queryFundBuyStatusListParam.setPartnersNumber(partnersNumber);
                                queryFundBuyStatusListParam.setHzOnWayAgentDealDtlMap(hzOnWayAgentDealDtlMap);
                                queryFundBuyStatusListParam.setHzConfirmBalanceBaseInfoMap(hzConfirmBalanceBaseInfoMap);
                                return fundBuyStatusService.queryProcess(queryFundBuyStatusListParam);
                            }), queryBuyFundStatusNewFacadeExecutor))
                    .collect(Collectors.toList());
            fundBuyStatusDtos = completableFutures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList());
            long endTime = System.currentTimeMillis();
            logger.info("线程处理总耗时{}", endTime - startTime);

        }
        return fundBuyStatusDtos;
    }

    /**
     * 获取好臻确认份额
     *
     * @param txAcctNo
     * @param hzFundCodeList
     * @return
     */
    private Map<String, List<AcctBalanceBaseInfo>> getHzConfirmBalanceBaseInfoMap(String txAcctNo, List<String> hzFundCodeList) {
        Map<String, List<AcctBalanceBaseInfo>> hzConfirmBalanceBaseInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hzFundCodeList)) {
            // 获取好臻确认份额
            QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
            queryAcctBalanceBaseParam.setTxAcctNo(txAcctNo);
            queryAcctBalanceBaseParam.setFundCodeList(hzFundCodeList);
            queryAcctBalanceBaseParam.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
            queryAcctBalanceBaseParam.setIncludeDirect(false);
            List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = acctBalanceBaseInfoService.queryConfirmBalanceBaseInfo(queryAcctBalanceBaseParam);
            hzConfirmBalanceBaseInfoMap = confirmBalanceBaseInfoList.stream().collect(Collectors.groupingBy(AcctBalanceBaseInfo::getFundCode));
        }
        return hzConfirmBalanceBaseInfoMap;
    }

    /**
     * 获取好臻在途信息
     *
     * @param txAcctNo
     * @param hzFundCodeList
     * @return
     */
    private Map<String, List<HighDealOrderDtlPo>> getHzOnWayAgentDealDtlMap(String txAcctNo, List<String> hzFundCodeList) {
        Map<String, List<HighDealOrderDtlPo>> hzOnWayAgentDealDtlMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(hzFundCodeList)) {
            // 获取好臻在途信息
            QueryHighDealOrderParam param = new QueryHighDealOrderParam();
            param.setTxAcctNo(txAcctNo);
            param.setFundCodeList(hzFundCodeList);
            param.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
            List<HighDealOrderDtlPo> onWayAgentDealDtlList = highDealOrderDtlRepository.getOnWayAgentDealDtlList(param);
            // 按照产品代码归集,key为产品代码,value为HighDealOrderDtlPo列表
            hzOnWayAgentDealDtlMap = onWayAgentDealDtlList.stream().collect(Collectors.groupingBy(HighDealOrderDtlPo::getFundCode));
        }
        return hzOnWayAgentDealDtlMap;
    }


    /**
     * 获取缓存的keys
     */
    private String[] getBatchProductTotalKeys(List<String> fundCodes) {
        return fundCodes.stream().map(this::geProductTotalKey).toArray(String[]::new);
    }

    private String geProductTotalKey(String fundCode) {
        return CacheKeyPrefix.MIDDLE_PRODUCT_SALE + "PRODUCTTOTALNEW_" + fundCode;
    }

    private void processBlack(QueryBuyFundStatusRequest request, QueryBuyFundStatusResponse response) {
        // 结果数据
        List<BuyFundStatusBean> list = new ArrayList<BuyFundStatusBean>();
        response.setBuyFundStatusList(list);
        BuyFundStatusBean bean = null;
        for (String productCode : request.getProductCodeList()) {
            bean = new BuyFundStatusBean();
            bean.setProductCode(productCode);
            bean.setShareClass(ShareClassEnum.A.getCode());
            // 不可购买
            bean.setBuyStatus(BuyStatusEnum.NOT_ALLOW.getCode());
            // 已售罄
            bean.setBuyStatusType(BuyStatusTypeEnum.SOLD_OUT.getCode());
            list.add(bean);
        }
    }

    private void checkParam(QueryBuyFundStatusRequest request, QueryBuyFundStatusResponse response) {
        List<String> productCodeList = request.getProductCodeList();
        if (CollectionUtils.isEmpty(productCodeList)) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("产品代码列表不能为空");
            return;
        }

        if (productCodeList.size() > maxCount) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("产品代码列表超限");
            return;
        }
        if (NO_TX_ACCT_NO.equals(request.getTxAcctNo())) {
            logger.info("QueryBuyFundStatusNewFacadeService-checkParam,交易账号为特定默认空值,该是为了避开切面强校验,将交易账号置空");
            request.setTxAcctNo(null);
            return;
        }
        if (StringUtils.isBlank(request.getTxAcctNo()) && StringUtils.isBlank(request.getHbOneNo())) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("交易账号与一账通不能同时为空");
            return;
        }
        if (StringUtils.isBlank(request.getTxAcctNo())) {
            String txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(request.getHbOneNo());
            if (StringUtils.isBlank(txAcctNo)) {
                response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
                response.setDescription("根据一账通查询不到交易账号");
                return;
            }
            request.setTxAcctNo(txAcctNo);
        }
    }


}