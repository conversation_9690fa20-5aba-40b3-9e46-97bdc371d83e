<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.finonline</groupId>
    <artifactId>fin-online</artifactId>
    <version>3.2.5-RELEASE</version>
    <packaging>pom</packaging>
    <name>fin-online</name>

    <properties>
        
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        
        <dubbo.version>2.7.15</dubbo.version>
        <druid.version>0.2.9</druid.version>
        <mybatis.version>3.4.1</mybatis.version>
        <fastjson.version>1.1.41</fastjson.version>
        <ojdbc6.version>11.2.0.2.0</ojdbc6.version>
        <zkclient.version>0.4</zkclient.version>
        <pagehelper.version>4.2.1</pagehelper.version>
        <mybatis-spring.version>1.3.1</mybatis-spring.version>
        <ch.qos.logback.version>1.2.3</ch.qos.logback.version>
        <javax.servlet.version>3.1.0</javax.servlet.version>
        <junit.version>4.12</junit.version>
        <com.howbuy.common.version>RELEASE</com.howbuy.common.version>
        <com.howbuy.fin-online.version>3.2.5-RELEASE</com.howbuy.fin-online.version>
        <com.howbuy.fin-console.version>3.2.5-RELEASE</com.howbuy.fin-console.version>
        <acc-center.version>RELEASE</acc-center.version>
        <com.howbuy.acc-common-utils.version>3.5.8-RELEASE</com.howbuy.acc-common-utils.version>
        <com.howbuy.fbs-online-search-facade.version>3.38.0-RELEASE</com.howbuy.fbs-online-search-facade.version>
        <com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
        <com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
        <com.howbuy.common-config.version>3.5.7-RELEASE</com.howbuy.common-config.version>
        <com.howbuy.howbuy-auth-facade.version>6.17.6-RELEASE</com.howbuy.howbuy-auth-facade.version>
        <spring-boot.version>2.3.12.RELEASE</spring-boot.version>
        <com.howbuy.howbuy-boot-actuator.version>1.1.6-RELEASE</com.howbuy.howbuy-boot-actuator.version>
</properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Hoxton.SR12</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2.2.8.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.nacos</groupId>
                        <artifactId>nacos-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.finonline</groupId>
                <artifactId>fin-online-common</artifactId>
                <version>${com.howbuy.fin-online.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.finonline</groupId>
                <artifactId>fin-online-facade</artifactId>
                <version>${com.howbuy.fin-online.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.finonline</groupId>
                <artifactId>fin-online-service</artifactId>
                <version>${com.howbuy.fin-online.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.finonline</groupId>
                <artifactId>fin-online-dao</artifactId>
                <version>${com.howbuy.fin-online.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.common</groupId>
                <artifactId>common-service</artifactId>
                <version>${com.howbuy.common-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.common</groupId>
                <artifactId>common-facade</artifactId>
                <version>${com.howbuy.common-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.common</groupId>
                <artifactId>common-config</artifactId>
                <version>${com.howbuy.common-config.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.finconsole</groupId>
                <artifactId>fin-console-facade</artifactId>
                <version>${com.howbuy.fin-console.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.druid</groupId>
                <artifactId>druid-wrapper</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>${ojdbc6.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jboss.netty</groupId>
                        <artifactId>netty</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.4.13</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.26.0-GA</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>utils</artifactId>
                <version>1.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.tomcat</groupId>
                        <artifactId>tomcat-jdbc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.tomcat</groupId>
                        <artifactId>juli</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.wltea</groupId>
                        <artifactId>IKAnalyzer</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>net.sourceforge.pinyin4j</groupId>
                        <artifactId>pinyin4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            

            <dependency>
                <groupId>com.101tec</groupId>
                <artifactId>zkclient</artifactId>
                <version>${zkclient.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${ch.qos.logback.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.21</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-auth-facade</artifactId>
                <version>${com.howbuy.howbuy-auth-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>20.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator</artifactId>
                <version>${com.howbuy.howbuy-boot-actuator.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.rocketmq</groupId>
                        <artifactId>rocketmq-tools</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.servlet.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.fbs</groupId>
                <artifactId>fbs-online-search-facade</artifactId>
                <version>${com.howbuy.fbs-online-search-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.acc</groupId>
                <artifactId>acc-common-utils</artifactId>
                <version>${com.howbuy.acc-common-utils.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.howbuy.acc</groupId>
                        <artifactId>acc-common-cache</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>maven2-repository.dev.java.net</id>
            <name>Java.net Repository for Maven</name>
            <url>http://download.java.net/maven/2/</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>ReleaseRepo</id>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/thirdparty</url>
        </repository>
        <repository>
            <id>nexus</id>
            <name>local private nexus</name>
            <url>http://maven.oschina.net/content/groups/public/</url>
            <releases>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>nexus</id>
            <name>local private nexus</name>
            <url>http://maven.oschina.net/content/groups/public/</url>
            <releases>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>nexus-release</id>
            <name>Nexus Releases Repository</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshots Repository</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
    </project>