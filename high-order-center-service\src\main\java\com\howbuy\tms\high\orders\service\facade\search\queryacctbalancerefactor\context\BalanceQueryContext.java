package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.context;

import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.QueryAcctBalanceRefactorRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @description: 持仓查询上下文
 * @author: hongdong.xie
 * @date: 2025/8/26 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class BalanceQueryContext {

    /**
     * 原始请求对象
     */
    private QueryAcctBalanceRefactorRequest request;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 一账通账号
     */
    private String hbOneNo;

    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;

    /**
     * 产品代码（单个产品查询时使用）
     */
    private String productCode;

    /**
     * 持仓状态
     * 0-不持仓，1-持仓，2-全部
     */
    private String balanceStatus;

    /**
     * 调用类型
     * 1-新资产中心，2-老资产中心
     */
    private String callType;

    /**
     * 清盘中产品列表
     */
    private List<String> crisisFundList;

    /**
     * 产品基本信息缓存
     * Key: 产品代码, Value: 产品基本信息
     */
    private Map<String, Object> productInfoCache;

    /**
     * 净值信息缓存
     * Key: 产品代码, Value: 净值信息
     */
    private Map<String, Object> navInfoCache;

    /**
     * 收益信息缓存
     * Key: 产品代码, Value: 收益信息
     */
    private Map<String, Object> incomeInfoCache;

    /**
     * 需要查询净值的产品代码集合
     */
    private Set<String> navQueryProductCodes;

    /**
     * 需要查询收益的产品代码集合
     */
    private Set<String> incomeQueryProductCodes;

    /**
     * 需要查询回款的产品代码集合（股权产品）
     */
    private Set<String> cashCollectionQueryProductCodes;

    /**
     * 特殊产品指标控制配置
     * Key: 产品代码, Value: 控制配置列表
     */
    private Map<String, List<Object>> productFieldControlMap;
}
