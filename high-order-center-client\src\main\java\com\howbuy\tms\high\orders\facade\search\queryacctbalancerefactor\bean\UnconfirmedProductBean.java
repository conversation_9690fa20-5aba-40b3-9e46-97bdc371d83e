package com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 在途产品Bean（重构版）
 * @author: hongdong.xie
 * @date: 2025/8/26 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class UnconfirmedProductBean implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 在途金额（人民币）
     */
    private BigDecimal unconfirmedAmt;

    /**
     * 在途份额
     */
    private BigDecimal unconfirmedVol;

    /**
     * 业务类型
     * 1-申购，2-赎回
     */
    private String businessType;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 申请日期
     * 格式：YYYYMMDD
     */
    private String appDt;

    /**
     * 分销机构代码
     */
    private String disCode;
}
