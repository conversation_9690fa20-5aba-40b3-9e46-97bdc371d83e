package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.converter;

import com.howbuy.tms.common.enums.busi.ScaleTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.high.orders.dao.po.CmCustFundDirectPo;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.BalanceInfoBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 持仓数据转换器
 * @author: hongdong.xie
 * @date: 2025/8/26 15:00
 * @since JDK 1.8
 */
@Slf4j
@Component
public class BalanceDataConverter {

    /**
     * @description: 从代销持仓VO转换为持仓信息Bean
     * @param balanceVo 代销持仓VO
     * @param productInfo 产品基本信息
     * @param disCodeList 分销机构代码列表
     * @return BalanceInfoBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:00
     * @since JDK 1.8
     */
    public BalanceInfoBean convertFromBalanceVo(BalanceVo balanceVo, Object productInfo, List<String> disCodeList) {
        HighProductDBInfoBean productBean = (HighProductDBInfoBean) productInfo;
        
        BalanceInfoBean balanceBean = new BalanceInfoBean();
        
        // 基本信息
        balanceBean.setDisCode(balanceVo.getDisCode());
        balanceBean.setDisCodeList(disCodeList);
        balanceBean.setProductCode(balanceVo.getProductCode());
        balanceBean.setProductName(productBean.getFundAttr());
        balanceBean.setProductType(productBean.getFundType());
        balanceBean.setProductSubType(productBean.getFundSubType());
        balanceBean.setCurrency(productBean.getCurrency());
        balanceBean.setScaleType(ScaleTypeEnum.CONSIGNMENT.getCode());
        
        // 持仓数据
        balanceBean.setBalanceVol(balanceVo.getBalanceVol());
        balanceBean.setUnconfirmedVol(balanceVo.getUnconfirmedVol());
        balanceBean.setUnconfirmedAmt(balanceVo.getUnconfirmedAmt());
        balanceBean.setNetBuyAmount(balanceVo.getNetBuyAmount());
        
        // 产品特殊标识
        balanceBean.setHkSaleFlag(productBean.getHkSaleFlag());
        balanceBean.setStageEstablishFlag(productBean.getStageEstablishFlag());
        balanceBean.setFractionateCallFlag(productBean.getFractionateCallFlag());
        balanceBean.setNavDisclosureType(productBean.getNavDisclosureType());
        
        // 监管分类
        balanceBean.setOneStepType(productBean.getOneStepType());
        balanceBean.setTwoStepType(productBean.getTwoStepType());
        balanceBean.setSecondStepType(productBean.getSecondStepType());
        
        // NA产品收费类型
        balanceBean.setNaProductFeeType(productBean.getNaProductFeeType());
        
        // 初始化状态
        balanceBean.setBalance(balanceVo.getBalanceVol() != null && balanceVo.getBalanceVol().compareTo(java.math.BigDecimal.ZERO) > 0);
        balanceBean.setCrisisFlag(YesOrNoEnum.NO.getCode());
        balanceBean.setNavDivFlag(YesOrNoEnum.NO.getCode());
        
        return balanceBean;
    }

    /**
     * @description: 从直销持仓PO转换为持仓信息Bean
     * @param directPo 直销持仓PO
     * @param productInfo 产品基本信息
     * @param disCodeList 分销机构代码列表
     * @return BalanceInfoBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:00
     * @since JDK 1.8
     */
    public BalanceInfoBean convertFromDirectPo(CmCustFundDirectPo directPo, Object productInfo, List<String> disCodeList) {
        HighProductDBInfoBean productBean = (HighProductDBInfoBean) productInfo;
        
        BalanceInfoBean balanceBean = new BalanceInfoBean();
        
        // 基本信息
        balanceBean.setDisCode(directPo.getDiscode());
        balanceBean.setDisCodeList(disCodeList);
        balanceBean.setProductCode(directPo.getFundcode());
        balanceBean.setProductName(directPo.getFundname());
        balanceBean.setProductType(directPo.getFundtype());
        balanceBean.setProductSubType(productBean.getFundSubType());
        balanceBean.setCurrency(directPo.getCurrency());
        balanceBean.setScaleType(ScaleTypeEnum.DIRECT.getCode());
        
        // 持仓数据
        balanceBean.setBalanceVol(directPo.getBalancevol());
        // 直销产品的净购买金额需要通过交易记录计算
        // balanceBean.setNetBuyAmount(calculateNetBuyAmount(directPo));
        
        // 产品特殊标识
        balanceBean.setHkSaleFlag(directPo.getIsHkProduct());
        balanceBean.setStageEstablishFlag(productBean.getStageEstablishFlag());
        balanceBean.setFractionateCallFlag(productBean.getFractionateCallFlag());
        balanceBean.setNavDisclosureType(productBean.getNavDisclosureType());
        
        // 监管分类
        balanceBean.setOneStepType(productBean.getOneStepType());
        balanceBean.setTwoStepType(productBean.getTwoStepType());
        balanceBean.setSecondStepType(productBean.getSecondStepType());
        
        // NA产品收费类型
        balanceBean.setNaProductFeeType(productBean.getNaProductFeeType());
        
        // 初始化状态
        balanceBean.setBalance(directPo.getBalancevol() != null && directPo.getBalancevol().compareTo(java.math.BigDecimal.ZERO) > 0);
        balanceBean.setCrisisFlag(YesOrNoEnum.NO.getCode());
        balanceBean.setNavDivFlag(YesOrNoEnum.NO.getCode());
        
        return balanceBean;
    }

    /**
     * @description: 从子账本PO转换为持仓信息Bean（分期成立产品）
     * @param subBooksPo 子账本PO
     * @param productInfo 产品基本信息
     * @param disCodeList 分销机构代码列表
     * @return BalanceInfoBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:00
     * @since JDK 1.8
     */
    public BalanceInfoBean convertFromSubBooksPo(SubCustBooksPo subBooksPo, Object productInfo, List<String> disCodeList) {
        HighProductDBInfoBean productBean = (HighProductDBInfoBean) productInfo;
        
        BalanceInfoBean balanceBean = new BalanceInfoBean();
        
        // 基本信息
        balanceBean.setDisCode(subBooksPo.getDisCode());
        balanceBean.setDisCodeList(disCodeList);
        balanceBean.setProductCode(subBooksPo.getFundCode());
        balanceBean.setSubProductCode(subBooksPo.getSubFundCode());
        balanceBean.setProductName(productBean.getFundAttr());
        balanceBean.setProductType(productBean.getFundType());
        balanceBean.setProductSubType(productBean.getFundSubType());
        balanceBean.setCurrency(productBean.getCurrency());
        balanceBean.setScaleType(ScaleTypeEnum.CONSIGNMENT.getCode());
        
        // 持仓数据
        balanceBean.setBalanceVol(subBooksPo.getBalanceVol());
        balanceBean.setUnconfirmedVol(subBooksPo.getFrznVol());
        balanceBean.setNetBuyAmount(subBooksPo.getNetBuyAmount());
        
        // 分期成立产品特殊字段
        balanceBean.setStageEstablishFlag(YesOrNoEnum.YES.getCode());
        balanceBean.setHkSaleFlag(productBean.getHkSaleFlag());
        balanceBean.setFractionateCallFlag(productBean.getFractionateCallFlag());
        balanceBean.setNavDisclosureType(productBean.getNavDisclosureType());
        
        // 开放赎回日期
        if (subBooksPo.getOpenRedeDt() != null) {
            balanceBean.setIncomeDt(subBooksPo.getOpenRedeDt()); // 临时用收益日期字段存储开放赎回日期
        }
        
        // 监管分类
        balanceBean.setOneStepType(productBean.getOneStepType());
        balanceBean.setTwoStepType(productBean.getTwoStepType());
        balanceBean.setSecondStepType(productBean.getSecondStepType());
        
        // NA产品收费类型
        balanceBean.setNaProductFeeType(productBean.getNaProductFeeType());
        
        // 初始化状态
        balanceBean.setBalance(subBooksPo.getBalanceVol() != null && subBooksPo.getBalanceVol().compareTo(java.math.BigDecimal.ZERO) > 0);
        balanceBean.setCrisisFlag(YesOrNoEnum.NO.getCode());
        balanceBean.setNavDivFlag(YesOrNoEnum.NO.getCode());
        
        return balanceBean;
    }

    /**
     * @description: 设置产品存续期限描述
     * @param balanceBean 持仓信息Bean
     * @param productInfo 产品基本信息
     * @author: hongdong.xie
     * @date: 2025/8/26 15:00
     * @since JDK 1.8
     */
    public void setProductDurationInfo(BalanceInfoBean balanceBean, Object productInfo) {
        HighProductDBInfoBean productBean = (HighProductDBInfoBean) productInfo;
        
        // 股权产品存续期限描述
        if (productBean.getFundCXQXStr() != null) {
            balanceBean.setFundCXQXStr(productBean.getFundCXQXStr());
        }
        
        // 投资期限
        if (productBean.getInvestmentHorizon() != null) {
            balanceBean.setInvestmentHorizon(productBean.getInvestmentHorizon());
        }
        
        // 起息日和到期日
        if (productBean.getValueDate() != null) {
            balanceBean.setValueDate(productBean.getValueDate());
        }
        if (productBean.getDueDate() != null) {
            balanceBean.setDueDate(productBean.getDueDate());
        }
    }
}
