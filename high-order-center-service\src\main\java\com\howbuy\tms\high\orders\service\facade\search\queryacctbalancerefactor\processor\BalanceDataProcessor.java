package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.BalanceInfoBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.context.BalanceQueryContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * @description: 持仓数据处理器
 * @author: hongdong.xie
 * @date: 2025/8/26 15:40
 * @since JDK 1.8
 */
@Slf4j
@Component
public class BalanceDataProcessor {

    /**
     * @description: 处理持仓数据
     * @param balanceList 持仓列表
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    public void processBalanceData(List<BalanceInfoBean> balanceList, BalanceQueryContext context) {
        if (CollectionUtils.isEmpty(balanceList)) {
            return;
        }

        log.info("开始处理持仓数据，数量：{}", balanceList.size());

        // 1. 过滤权限产品
        filterBalanceByAuth(balanceList, context);

        // 2. 设置DB配置
        processDbConfig(balanceList);

        // 3. 设置特殊字段值
        processSpecialFields(balanceList, context);

        // 4. 检查告警
        checkAlarm(balanceList, context);

        // 5. 查询特殊产品指标控制配置
        queryProductFieldControl(balanceList, context);

        log.info("持仓数据处理完成，最终数量：{}", balanceList.size());
    }

    /**
     * @description: 过滤权限产品（好臻/香港产品）
     * @param balanceList 持仓列表
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void filterBalanceByAuth(List<BalanceInfoBean> balanceList, BalanceQueryContext context) {
        Iterator<BalanceInfoBean> iterator = balanceList.iterator();
        int removedCount = 0;

        while (iterator.hasNext()) {
            BalanceInfoBean balanceBean = iterator.next();
            
            // 过滤好臻产品
            if (shouldFilterHzProduct(balanceBean, context)) {
                iterator.remove();
                removedCount++;
                continue;
            }

            // 过滤香港产品
            if (shouldFilterHkProduct(balanceBean, context)) {
                iterator.remove();
                removedCount++;
                continue;
            }
        }

        if (removedCount > 0) {
            log.info("权限过滤完成，移除{}条记录", removedCount);
        }
    }

    /**
     * @description: 判断是否应该过滤好臻产品
     * @param balanceBean 持仓信息Bean
     * @param context 查询上下文
     * @return boolean 是否应该过滤
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private boolean shouldFilterHzProduct(BalanceInfoBean balanceBean, BalanceQueryContext context) {
        // 如果不过滤好臻产品，则不过滤
        if (YesOrNoEnum.YES.getCode().equals(context.getRequest().getNotFilterHzFund())) {
            return false;
        }

        // 如果分销机构列表包含好臻，则不过滤好臻产品
        if (!CollectionUtils.isEmpty(context.getDisCodeList()) && 
            context.getDisCodeList().contains(DisCodeEnum.HZ.getCode())) {
            return false;
        }

        // 如果产品的分销机构是好臻，则过滤
        return DisCodeEnum.HZ.getCode().equals(balanceBean.getDisCode());
    }

    /**
     * @description: 判断是否应该过滤香港产品
     * @param balanceBean 持仓信息Bean
     * @param context 查询上下文
     * @return boolean 是否应该过滤
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private boolean shouldFilterHkProduct(BalanceInfoBean balanceBean, BalanceQueryContext context) {
        // 如果不过滤香港产品，则不过滤
        if (YesOrNoEnum.YES.getCode().equals(context.getRequest().getNotFilterHkFund())) {
            return false;
        }

        // 如果请求明确要查询香港产品，则不过滤
        if (YesOrNoEnum.YES.getCode().equals(context.getRequest().getHkSaleFlag())) {
            return false;
        }

        // 如果产品是香港产品，则过滤
        return YesOrNoEnum.YES.getCode().equals(balanceBean.getHkSaleFlag());
    }

    /**
     * @description: 设置DB配置
     * @param balanceList 持仓列表
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void processDbConfig(List<BalanceInfoBean> balanceList) {
        for (BalanceInfoBean balanceBean : balanceList) {
            // 设置产品DB配置信息
            // TODO: 根据产品类型设置相应的DB配置
            // 例如：设置产品的显示配置、计算配置等
            
            // 设置产品特殊标识
            setProductSpecialFlags(balanceBean);
        }
    }

    /**
     * @description: 设置产品特殊标识
     * @param balanceBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void setProductSpecialFlags(BalanceInfoBean balanceBean) {
        // 设置清盘中标识
        if (isCrisisProduct(balanceBean.getProductCode())) {
            balanceBean.setCrisisFlag(YesOrNoEnum.YES.getCode());
        }

        // 设置净值分红标识
        if (hasNavDividend(balanceBean.getProductCode())) {
            balanceBean.setNavDivFlag(YesOrNoEnum.YES.getCode());
        }
    }

    /**
     * @description: 设置特殊字段值
     * @param balanceList 持仓列表
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void processSpecialFields(List<BalanceInfoBean> balanceList, BalanceQueryContext context) {
        for (BalanceInfoBean balanceBean : balanceList) {
            // 设置收益计算状态
            setIncomeCalculationStatus(balanceBean, context);

            // 设置币种转换
            processCurrencyConversion(balanceBean);

            // 设置NA产品费用
            processNaProductFee(balanceBean);
        }
    }

    /**
     * @description: 设置收益计算状态
     * @param balanceBean 持仓信息Bean
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void setIncomeCalculationStatus(BalanceInfoBean balanceBean, BalanceQueryContext context) {
        // TODO: 根据产品类型和清盘状态设置收益计算状态
        // 股权产品、清盘产品等设置为计算中
        // 其他产品设置为计算完成
    }

    /**
     * @description: 处理币种转换
     * @param balanceBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void processCurrencyConversion(BalanceInfoBean balanceBean) {
        // TODO: 如果是外币产品，需要按汇率转换为人民币
        // 设置原币种金额和人民币金额
    }

    /**
     * @description: 处理NA产品费用
     * @param balanceBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void processNaProductFee(BalanceInfoBean balanceBean) {
        // TODO: 如果是NA产品（好买收费），需要扣除管理费和业绩报酬
        // 计算费后市值
    }

    /**
     * @description: 检查告警
     * @param balanceList 持仓列表
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void checkAlarm(List<BalanceInfoBean> balanceList, BalanceQueryContext context) {
        // TODO: 检查持仓告警
        // 例如：净值异常、收益异常等
        for (BalanceInfoBean balanceBean : balanceList) {
            checkProductAlarm(balanceBean, context);
        }
    }

    /**
     * @description: 检查单个产品告警
     * @param balanceBean 持仓信息Bean
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void checkProductAlarm(BalanceInfoBean balanceBean, BalanceQueryContext context) {
        // TODO: 实现具体的告警检查逻辑
        // 检查净值是否异常、收益是否异常等
    }

    /**
     * @description: 查询特殊产品指标控制配置
     * @param balanceList 持仓列表
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private void queryProductFieldControl(List<BalanceInfoBean> balanceList, BalanceQueryContext context) {
        // TODO: 查询特殊产品指标控制配置
        // 某些产品需要特殊的显示控制或计算控制
        Map<String, List<Object>> fieldControlMap = getProductFieldControlMap(balanceList);
        context.setProductFieldControlMap(fieldControlMap);
    }

    /**
     * @description: 获取产品指标控制配置
     * @param balanceList 持仓列表
     * @return Map<String, List<Object>> 产品指标控制配置Map
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private Map<String, List<Object>> getProductFieldControlMap(List<BalanceInfoBean> balanceList) {
        // TODO: 实现产品指标控制配置查询
        return new java.util.HashMap<>();
    }

    /**
     * @description: 判断是否为清盘中产品
     * @param productCode 产品代码
     * @return boolean 是否为清盘中产品
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private boolean isCrisisProduct(String productCode) {
        // TODO: 判断产品是否在清盘中
        return false;
    }

    /**
     * @description: 判断是否有净值分红
     * @param productCode 产品代码
     * @return boolean 是否有净值分红
     * @author: hongdong.xie
     * @date: 2025/8/26 15:40
     * @since JDK 1.8
     */
    private boolean hasNavDividend(String productCode) {
        // TODO: 判断产品是否有净值分红
        return false;
    }
}
