package com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.constant.TxCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 查询客户持仓请求（重构版）
 * @author: hongdong.xie
 * @date: 2025/8/26 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryAcctBalanceRefactorRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 1L;

    public QueryAcctBalanceRefactorRequest() {
        setTxCode(TxCodes.HIGH_FUND_QUERY_ACCT_BALANCE);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 好买香港代销标识
     * 0-否，1-是
     */
    private String hkSaleFlag;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品子类型
     * 1-股权，2-固定收益，3-现金管理等
     */
    private String productSubType;

    /**
     * 协议类型
     * 4-高端产品协议
     */
    private String protocolType = "4";

    /**
     * 分销机构代码列表
     * 股权直销改造
     */
    private List<String> disCodeList;

    /**
     * 调用类型
     * 1-新资产中心，2-老资产中心
     */
    private String callType = "2";

    /**
     * 持仓状态
     * 0-不持仓，1-持仓，2-全部，默认查持仓
     */
    private String balanceStatus = "1";

    /**
     * 不过滤香港产品
     * 1-是，0-否
     */
    private String notFilterHkFund;

    /**
     * 不过滤好臻产品
     * 1-是，0-否
     */
    private String notFilterHzFund;
}
