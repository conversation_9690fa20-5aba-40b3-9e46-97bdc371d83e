package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo;

import com.howbuy.tms.high.orders.service.facade.search.queryacctbalance.bean.OwnershipOrderDto;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.*;

import java.util.List;
import java.util.Map;

/**
 * @Description:用户持仓基础信息接口
 * @Author: yun.lu
 * Date: 2023/8/16 14:05
 */
public interface AcctBalanceBaseInfoService {

    /**
     * 查询股权订单信息
     */
    List<OwnershipOrderInfo> queryAcctOwnershipOrderInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseParam);


    /**
     * 查询用户持仓基础信息
     */
    List<AcctBalanceBaseInfo> queryAcctBalanceBaseInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseInfoParam);


    /**
     * 查询用户持仓明细基础信息
     */
    List<AcctBalanceDetailBaseInfo> queryAcctBalanceDetailBaseInfo(QueryAcctBalanceDetailBaseInfoParam queryAcctBalanceDetailBaseInfoParam);

    /**
     * 查询用户持仓明细基础信息
     */
    List<AcctSubBalanceDetailInfo> queryAcctSubBalanceDetailInfo(QueryAcctSubBalanceDetailParam param);

    /**
     * 查询在途持仓信息,已付款订单
     *
     * @param queryAcctBalanceBaseInfoParam 在途持仓信息查询入参
     * @return 持仓基本信息
     */
    List<AcctBalanceBaseInfo> queryOnWayBalanceBaseInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseInfoParam);


    /**
     * 查询确认持仓信息
     *
     * @param queryAcctBalanceBaseInfoParam 在途持仓信息查询入参
     * @return 持仓基本信息
     */
    List<AcctBalanceBaseInfo> queryConfirmBalanceBaseInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseInfoParam);

    /**
     * 查询股权产品订单信息
     * @param queryAcctBalanceBaseParam
     * @return
     */
    Map<String, OwnershipOrderDto> getOwnershipOrderInfoMap(QueryAcctBalanceBaseParam queryAcctBalanceBaseParam);

    /**
     * 是否首单,好臻分次call首次只跟确认持仓有关
     * @param fundCode 基金代码
     * @param txAcctNo 交易账号
     * @return 是否是首单,1:是 0:不是
     */
    String getIsFirstBuy(String fundCode, String txAcctNo, String disCode);


    /**
     * 待资金到账订单信息
     * @param queryBalanceParam 查询入参
     * @return 待资金到账订单信息
     */
    List<RefundDealOrderInfo> queryRefundAmtDealOrderInfo(QueryBalanceParam queryBalanceParam);

}
