package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor;

import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.enums.busi.ScaleTypeEnum;
import com.howbuy.tms.common.enums.busi.StageEstablishFlagEnum;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.BalanceInfoBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.context.BalanceQueryContext;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.converter.BalanceDataConverter;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.SubCustBooksRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description: 代销持仓处理器
 * @author: hongdong.xie
 * @date: 2025/8/26 14:30
 * @since JDK 1.8
 */
@Slf4j
@Component
public class ConsignmentBalanceProcessor {

    @Autowired
    private CustBooksRepository custBooksRepository;

    @Autowired
    private SubCustBooksRepository subCustBooksRepository;

    @Autowired
    private BalanceDataConverter balanceDataConverter;

    @Autowired
    private ProductInfoProcessor productInfoProcessor;

    @Autowired
    private MarketValueCalculator marketValueCalculator;

    /**
     * @description: 处理代销持仓
     * @param context 查询上下文
     * @return List<BalanceInfoBean> 代销持仓列表
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    public List<BalanceInfoBean> processConsignmentBalance(BalanceQueryContext context) {
        log.info("开始处理代销持仓，txAcctNo:{}, hbOneNo:{}", context.getTxAcctNo(), context.getHbOneNo());

        // 1. 查询代销持仓数据
        List<BalanceVo> balanceVoList = queryConsignmentBalance(context);
        if (CollectionUtils.isEmpty(balanceVoList)) {
            log.info("未查询到代销持仓数据");
            return new ArrayList<>();
        }

        // 2. 过滤特殊用户的特定产品
        balanceVoList = filterSpecialUserProducts(balanceVoList, context);
        if (CollectionUtils.isEmpty(balanceVoList)) {
            log.info("过滤后无代销持仓数据");
            return new ArrayList<>();
        }

        // 3. 获取产品代码集合
        Set<String> productCodeSet = balanceVoList.stream()
                .map(BalanceVo::getProductCode)
                .collect(Collectors.toSet());

        // 4. 批量查询产品基本信息
        Map<String, Object> productInfoMap = productInfoProcessor.batchQueryProductInfo(productCodeSet);

        // 5. 根据请求条件过滤产品
        productInfoMap = productInfoProcessor.filterProductsByRequest(productInfoMap, context.getRequest());

        // 6. 处理非结构化产品
        List<BalanceInfoBean> notStructureBalanceList = processNotStructureProducts(balanceVoList, productInfoMap, context);

        // 7. 处理结构化产品（分期成立）
        List<BalanceInfoBean> structureBalanceList = processStructureProducts(balanceVoList, productInfoMap, context);

        // 8. 合并结果
        List<BalanceInfoBean> allBalanceList = new ArrayList<>();
        allBalanceList.addAll(notStructureBalanceList);
        allBalanceList.addAll(structureBalanceList);

        log.info("代销持仓处理完成，共{}条记录", allBalanceList.size());
        return allBalanceList;
    }

    /**
     * @description: 查询代销持仓数据
     * @param context 查询上下文
     * @return List<BalanceVo> 持仓数据列表
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private List<BalanceVo> queryConsignmentBalance(BalanceQueryContext context) {
        List<String> productCodeList = null;
        if (StringUtils.hasText(context.getProductCode())) {
            productCodeList = Collections.singletonList(context.getProductCode());
        }

        return custBooksRepository.selectBalanceWithLockPeriod(
                context.getDisCodeList(),
                context.getTxAcctNo(),
                productCodeList,
                context.getBalanceStatus()
        );
    }

    /**
     * @description: 过滤特殊用户的特定产品
     * @param balanceVoList 原始持仓列表
     * @param context 查询上下文
     * @return List<BalanceVo> 过滤后的持仓列表
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private List<BalanceVo> filterSpecialUserProducts(List<BalanceVo> balanceVoList, BalanceQueryContext context) {
        // 特定用户的特定产品不展示（业务需求）
        if ("1297653362".equals(context.getTxAcctNo()) || "8003718407".equals(context.getHbOneNo())) {
            return balanceVoList.stream()
                    .filter(vo -> !"PE0054".equals(vo.getProductCode()))
                    .collect(Collectors.toList());
        }
        return balanceVoList;
    }

    /**
     * @description: 处理非结构化产品
     * @param balanceVoList 持仓数据列表
     * @param productInfoMap 产品信息Map
     * @param context 查询上下文
     * @return List<BalanceInfoBean> 非结构化产品持仓列表
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private List<BalanceInfoBean> processNotStructureProducts(List<BalanceVo> balanceVoList,
                                                              Map<String, Object> productInfoMap,
                                                              BalanceQueryContext context) {
        List<BalanceInfoBean> resultList = new ArrayList<>();

        for (BalanceVo balanceVo : balanceVoList) {
            Object productInfo = productInfoMap.get(balanceVo.getProductCode());
            if (productInfo == null) {
                continue;
            }

            // 判断是否为结构化产品（分期成立）
            if (isStructureProduct(productInfo)) {
                continue; // 结构化产品单独处理
            }

            // 转换为BalanceInfoBean
            BalanceInfoBean balanceBean = balanceDataConverter.convertFromBalanceVo(balanceVo, productInfo, context.getDisCodeList());
            balanceBean.setScaleType(ScaleTypeEnum.CONSIGNMENT.getCode());

            // 计算市值和收益
            marketValueCalculator.calculateMarketValue(balanceBean, context);

            resultList.add(balanceBean);
        }

        return resultList;
    }

    /**
     * @description: 处理结构化产品（分期成立）
     * @param balanceVoList 持仓数据列表
     * @param productInfoMap 产品信息Map
     * @param context 查询上下文
     * @return List<BalanceInfoBean> 结构化产品持仓列表
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private List<BalanceInfoBean> processStructureProducts(List<BalanceVo> balanceVoList,
                                                           Map<String, Object> productInfoMap,
                                                           BalanceQueryContext context) {
        List<BalanceInfoBean> resultList = new ArrayList<>();

        // 获取结构化产品代码列表
        List<String> structureProductCodes = balanceVoList.stream()
                .filter(vo -> {
                    Object productInfo = productInfoMap.get(vo.getProductCode());
                    return productInfo != null && isStructureProduct(productInfo);
                })
                .map(BalanceVo::getProductCode)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(structureProductCodes)) {
            return resultList;
        }

        // 查询子账本数据
        // TODO: 实现子账本查询逻辑
        // List<SubCustBooksPo> subCustBooksPoList = subCustBooksRepository.selectSubCustBooks(...)

        return resultList;
    }

    /**
     * @description: 判断是否为结构化产品
     * @param productInfo 产品信息
     * @return boolean 是否为结构化产品
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private boolean isStructureProduct(Object productInfo) {
        // TODO: 根据产品信息判断是否为分期成立的结构化产品
        // 判断逻辑：ProductDBTypeEnum.GUDINGSHOUYI + StageEstablishFlagEnum.STAGE
        return false;
    }
}
