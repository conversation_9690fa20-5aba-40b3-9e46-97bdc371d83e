package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.IncomeCalStatEnum;
import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.util.BigUtil;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.QueryAcctBalanceRefactorResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.BalanceInfoBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.UnconfirmedProductBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.context.BalanceQueryContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @description: 资产汇总处理器
 * @author: hongdong.xie
 * @date: 2025/8/26 15:50
 * @since JDK 1.8
 */
@Slf4j
@Component
public class BalanceSummaryProcessor {

    /**
     * @description: 处理资产汇总
     * @param response 响应对象
     * @param balanceList 持仓列表
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:50
     * @since JDK 1.8
     */
    public void processSummary(QueryAcctBalanceRefactorResponse response, List<BalanceInfoBean> balanceList, BalanceQueryContext context) {
        log.info("开始处理资产汇总，持仓数量：{}", balanceList.size());

        // 1. 初始化汇总变量
        BigDecimal totalMarketValue = BigDecimal.ZERO; // 总市值
        BigDecimal totalCurrentAsset = BigDecimal.ZERO; // 总收益
        BigDecimal totalCashCollection = BigDecimal.ZERO; // 总回款金额（股权产品）
        String totalIncomCalStat = IncomeCalStatEnum.FINISHED.getCode(); // 总收益计算状态
        Set<String> hasProcessedFixedIncomeProductCodes = new HashSet<>(); // 已处理的固收产品代码

        // 2. 遍历持仓列表进行汇总
        for (BalanceInfoBean balanceBean : balanceList) {
            // 判断是否需要排除汇总
            boolean needExclude = shouldExcludeFromSummary(balanceBean, context);

            // 股权产品特殊处理：市值=净购买金额
            if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
                balanceBean.setMarketValue(balanceBean.getNetBuyAmount());
                balanceBean.setCurrencyMarketValue(balanceBean.getCurrencyNetBuyAmount());
            }

            // 固收产品去重处理
            if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())) {
                if (hasProcessedFixedIncomeProductCodes.contains(balanceBean.getProductCode())) {
                    continue; // 跳过重复的固收产品
                }
                hasProcessedFixedIncomeProductCodes.add(balanceBean.getProductCode());
            }

            // 累加总市值（只统计有持仓的产品）
            if (balanceBean.getMarketValue() != null && !needExclude && balanceBean.isBalance()) {
                totalMarketValue = totalMarketValue.add(balanceBean.getMarketValue());
            }

            // 累加总收益
            if (balanceBean.getCurrentAsset() != null && !needExclude && balanceBean.isBalance()) {
                totalCurrentAsset = totalCurrentAsset.add(balanceBean.getCurrentAsset());
            }

            // 累加总回款（股权产品）
            if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType()) &&
                balanceBean.getCashCollection() != null && !needExclude && balanceBean.isBalance()) {
                totalCashCollection = totalCashCollection.add(balanceBean.getCashCollection());
            }

            // 更新收益计算状态：如果有任何产品在计算中，总状态就是计算中
            if (IncomeCalStatEnum.CALCULATING.getCode().equals(balanceBean.getIncomeCalStat())) {
                totalIncomCalStat = IncomeCalStatEnum.CALCULATING.getCode();
            }

            // 检查是否持有特殊产品
            checkSpecialProducts(response, balanceBean);
        }

        // 3. 查询在途金额
        List<UnconfirmedProductBean> unconfirmedProducts = queryUnconfirmedProducts(context);
        BigDecimal totalUnconfirmedAmt = calculateTotalUnconfirmedAmount(unconfirmedProducts);

        // 4. 设置汇总结果
        response.setTotalMarketValue(BigUtil.formatMoney(totalMarketValue.add(totalUnconfirmedAmt), 2));
        response.setTotalCurrentAsset(BigUtil.formatMoney(totalCurrentAsset, 2));
        response.setTotalCashCollection(BigUtil.formatMoney(totalCashCollection, 2));
        response.setTotalIncomCalStat(totalIncomCalStat);
        response.setTotalUnconfirmedAmt(BigUtil.formatMoney(totalUnconfirmedAmt, 2));
        response.setTotalUnconfirmedNum(unconfirmedProducts.size());
        response.setUnconfirmedProducts(unconfirmedProducts);

        // 5. 统计赎回在途产品数量
        int redeemUnconfirmedNum = (int) unconfirmedProducts.stream()
                .filter(product -> "2".equals(product.getBusinessType())) // 2-赎回
                .count();
        response.setRedeemUnconfirmedNum(redeemUnconfirmedNum);

        log.info("资产汇总处理完成，总市值：{}，总收益：{}，总回款：{}，在途金额：{}", 
                response.getTotalMarketValue(), response.getTotalCurrentAsset(), 
                response.getTotalCashCollection(), response.getTotalUnconfirmedAmt());
    }

    /**
     * @description: 判断是否应该从汇总中排除
     * @param balanceBean 持仓信息Bean
     * @param context 查询上下文
     * @return boolean 是否应该排除
     * @author: hongdong.xie
     * @date: 2025/8/26 15:50
     * @since JDK 1.8
     */
    private boolean shouldExcludeFromSummary(BalanceInfoBean balanceBean, BalanceQueryContext context) {
        // 1. 检查是否在特殊产品指标控制表中
        Map<String, List<Object>> fieldControlMap = context.getProductFieldControlMap();
        if (fieldControlMap != null && fieldControlMap.containsKey(balanceBean.getProductCode())) {
            List<Object> controlList = fieldControlMap.get(balanceBean.getProductCode());
            if (!CollectionUtils.isEmpty(controlList)) {
                return true;
            }
        }

        // 2. 检查异常标识
        // TODO: 如果产品标记为异常（abnormalFlag=1），则排除
        // if (YesOrNoEnum.YES.getCode().equals(balanceBean.getAbnormalFlag())) {
        //     return true;
        // }

        return false;
    }

    /**
     * @description: 检查特殊产品
     * @param response 响应对象
     * @param balanceBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:50
     * @since JDK 1.8
     */
    private void checkSpecialProducts(QueryAcctBalanceRefactorResponse response, BalanceInfoBean balanceBean) {
        // 检查是否持有好臻产品
        if (DisCodeEnum.HZ.getCode().equals(balanceBean.getDisCode())) {
            response.setHasHZProduct(YesOrNoEnum.YES.getCode());
        }

        // 检查是否持有香港产品
        if (YesOrNoEnum.YES.getCode().equals(balanceBean.getHkSaleFlag())) {
            response.setHasHKProduct(YesOrNoEnum.YES.getCode());
        }
    }

    /**
     * @description: 查询在途产品
     * @param context 查询上下文
     * @return List<UnconfirmedProductBean> 在途产品列表
     * @author: hongdong.xie
     * @date: 2025/8/26 15:50
     * @since JDK 1.8
     */
    private List<UnconfirmedProductBean> queryUnconfirmedProducts(BalanceQueryContext context) {
        // TODO: 查询在途产品（未确认的申购/赎回订单）
        // 查询条件：交易账号、订单状态等
        // 返回在途产品列表
        return new java.util.ArrayList<>();
    }

    /**
     * @description: 计算总在途金额
     * @param unconfirmedProducts 在途产品列表
     * @return BigDecimal 总在途金额
     * @author: hongdong.xie
     * @date: 2025/8/26 15:50
     * @since JDK 1.8
     */
    private BigDecimal calculateTotalUnconfirmedAmount(List<UnconfirmedProductBean> unconfirmedProducts) {
        if (CollectionUtils.isEmpty(unconfirmedProducts)) {
            return BigDecimal.ZERO;
        }

        return unconfirmedProducts.stream()
                .filter(product -> product.getUnconfirmedAmt() != null)
                .filter(product -> product.getUnconfirmedAmt().compareTo(BigDecimal.ZERO) > 0)
                .map(UnconfirmedProductBean::getUnconfirmedAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .abs(); // 使用绝对值避免负数影响
    }

    /**
     * @description: 计算收益率
     * @param balanceCost 投资成本
     * @param currentAsset 当前收益
     * @param configScale 配置的小数位数
     * @return BigDecimal 收益率
     * @author: hongdong.xie
     * @date: 2025/8/26 15:50
     * @since JDK 1.8
     */
    public BigDecimal calculateYieldRate(BigDecimal balanceCost, BigDecimal currentAsset, Integer configScale) {
        BigDecimal yieldRate;
        int scale = 4; // 默认4位
        if (configScale != null) {
            scale = configScale;
        }

        if (balanceCost == null || BigDecimal.ZERO.compareTo(balanceCost) == 0) {
            yieldRate = BigDecimal.ZERO;
        } else {
            // 收益为负数：向下进一保留4位小数
            if (currentAsset != null && BigDecimal.ZERO.compareTo(currentAsset) > 0) {
                yieldRate = currentAsset.divide(balanceCost, scale + 1, BigDecimal.ROUND_DOWN)
                                      .setScale(scale, BigDecimal.ROUND_UP);
            } else {
                // 收益为正数：直接截断保留4位小数
                yieldRate = (currentAsset == null ? BigDecimal.ZERO : currentAsset)
                           .divide(balanceCost, scale, BigDecimal.ROUND_DOWN);
            }
        }
        return yieldRate;
    }

    /**
     * @description: 设置收益率
     * @param balanceBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:50
     * @since JDK 1.8
     */
    public void setYieldRate(BalanceInfoBean balanceBean) {
        // 计算当前收益率
        BigDecimal yieldRate = calculateYieldRate(balanceBean.getNetBuyAmount(), balanceBean.getCurrentAsset(), null);
        balanceBean.setYieldRate(yieldRate);

        // 计算累计收益率
        BigDecimal accumYieldRate = calculateYieldRate(balanceBean.getNetBuyAmount(), balanceBean.getAccumIncome(), null);
        balanceBean.setAccumYieldRate(accumYieldRate);
    }
}
