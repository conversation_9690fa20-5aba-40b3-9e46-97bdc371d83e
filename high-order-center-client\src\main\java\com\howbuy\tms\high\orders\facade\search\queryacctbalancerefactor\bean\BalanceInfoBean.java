package com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 持仓信息Bean（重构版）
 * @author: hongdong.xie
 * @date: 2025/8/26 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class BalanceInfoBean implements Serializable, Comparable<BalanceInfoBean> {

    private static final long serialVersionUID = 1L;

    /**
     * 分销代码
     */
    private String disCode;

    /**
     * 分销代码列表
     */
    private List<String> disCodeList;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 子产品代码（分期成立产品使用）
     */
    private String subProductCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品子类型
     * 1-股权，2-固定收益，3-现金管理等
     */
    private String productSubType;

    /**
     * 总份额
     */
    private BigDecimal balanceVol;

    /**
     * 待确认份额
     */
    private BigDecimal unconfirmedVol;

    /**
     * 待确认金额
     */
    private BigDecimal unconfirmedAmt;

    /**
     * 币种
     */
    private String currency;

    /**
     * 净值
     */
    private BigDecimal nav;

    /**
     * 净值日期
     * 格式：YYYYMMDD
     */
    private String navDt;

    /**
     * 净值分红标识
     * 0-否，1-是
     */
    private String navDivFlag = "0";

    /**
     * 市值（人民币）
     */
    private BigDecimal marketValue;

    /**
     * 当前币种的市值
     */
    private BigDecimal currencyMarketValue;

    /**
     * 销售类型
     * 1-直销，2-代销
     */
    private String scaleType;

    /**
     * 好买香港代销标识
     * 0-否，1-是
     */
    private String hkSaleFlag;

    /**
     * 分期成立标识
     * 0-否，1-是（证券类产品）
     */
    private String stageEstablishFlag;

    /**
     * 分次call标识
     * 0-否，1-是（股权类产品）
     */
    private String fractionateCallFlag;

    /**
     * 净购买金额（投资成本）（人民币）
     */
    private BigDecimal netBuyAmount;

    /**
     * 净购买金额（投资成本）（当前币种）
     */
    private BigDecimal currencyNetBuyAmount;

    /**
     * 收益计算状态
     * 0-计算中，1-计算完成
     */
    private String incomeCalStat;

    /**
     * 当前收益（人民币）
     */
    private BigDecimal currentAsset;

    /**
     * 当前收益（当前币种）
     */
    private BigDecimal currentAssetCurrency;

    /**
     * 累计收益（人民币）
     */
    private BigDecimal accumIncome;

    /**
     * 累计收益（当前币种）
     */
    private BigDecimal accumIncomeCurrency;

    /**
     * 收益率
     */
    private BigDecimal yieldRate;

    /**
     * 累计收益率
     */
    private BigDecimal accumYieldRate;

    /**
     * 私募股权回款（人民币）
     */
    private BigDecimal cashCollection;

    /**
     * 私募股权回款（当前币种）
     */
    private BigDecimal currencyCashCollection;

    /**
     * 清盘中标识
     * 0-否，1-是
     */
    private String crisisFlag;

    /**
     * 七日年化收益
     */
    private BigDecimal yieldIncome;

    /**
     * 七日年化日期
     * 格式：YYYYMMDD
     */
    private String yieldIncomeDt;

    /**
     * 万份收益
     */
    private BigDecimal copiesIncome;

    /**
     * NA产品收费类型
     * 10201-好买收费，0-管理人收费
     */
    private String naProductFeeType;

    /**
     * 累计应收管理费
     */
    private BigDecimal receivManageFee;

    /**
     * 累计应收业绩报酬
     */
    private BigDecimal receivPreformFee;

    /**
     * NA产品费后市值（当前币种）
     */
    private BigDecimal currencyMarketValueExFee;

    /**
     * NA产品费后市值（人民币）
     */
    private BigDecimal marketValueExFee;

    /**
     * 净值披露方式
     * 1-净值，2-份额收益
     */
    private String navDisclosureType;

    /**
     * 是否持仓
     * true-有持仓，false-无持仓
     */
    private boolean balance;

    /**
     * 监管分类一级
     */
    private String oneStepType;

    /**
     * 监管分类二级
     */
    private String twoStepType;

    /**
     * 监管分类二级（新）
     */
    private String secondStepType;

    /**
     * 收益日期
     * 格式：YYYYMMDD
     */
    private String incomeDt;

    /**
     * 产品存续期限描述
     */
    private String fundCXQXStr;

    /**
     * 投资期限
     */
    private String investmentHorizon;

    /**
     * 起息日
     * 格式：YYYYMMDD
     */
    private String valueDate;

    /**
     * 到期日
     * 格式：YYYYMMDD
     */
    private String dueDate;

    @Override
    public int compareTo(BalanceInfoBean other) {
        return this.productCode.compareTo(other.productCode);
    }
}
