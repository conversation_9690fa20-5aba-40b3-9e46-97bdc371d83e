package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor;

import com.howbuy.tms.common.enums.busi.NavDisclosureTypeEnum;
import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavBean;
import com.howbuy.tms.common.util.MoneyUtil;
import com.howbuy.tms.high.orders.dao.vo.HighDealOrderDtlLatestAckVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.BalanceInfoBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.context.BalanceQueryContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 市值计算器
 * @author: hongdong.xie
 * @date: 2025/8/26 15:20
 * @since JDK 1.8
 */
@Slf4j
@Component
public class MarketValueCalculator {

    /**
     * @description: 计算市值
     * @param balanceBean 持仓信息Bean
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    public void calculateMarketValue(BalanceInfoBean balanceBean, BalanceQueryContext context) {
        log.debug("开始计算市值，产品代码：{}", balanceBean.getProductCode());

        try {
            // 1. 股权产品特殊处理：市值=净购买金额
            if (ProductDBTypeEnum.GUQUAN.getCode().equals(balanceBean.getProductSubType())) {
                calculateEquityMarketValue(balanceBean);
                return;
            }

            // 2. 获取净值信息
            HighProductNavBean navBean = getNavInfo(balanceBean.getProductCode(), context);
            if (navBean == null) {
                log.warn("未获取到净值信息，产品代码：{}", balanceBean.getProductCode());
                setDefaultMarketValue(balanceBean);
                return;
            }

            // 3. 设置净值信息
            setNavInfo(balanceBean, navBean);

            // 4. 根据产品类型计算市值
            if (isFixedIncomeShareRevenueProduct(balanceBean)) {
                // 固收份额收益类产品：市值=份额×1
                calculateShareRevenueMarketValue(balanceBean);
            } else {
                // 净值类产品：根据是否有最新确认订单选择计算方式
                calculateNavBasedMarketValue(balanceBean, navBean, context);
            }

            log.debug("市值计算完成，产品代码：{}，市值：{}", balanceBean.getProductCode(), balanceBean.getMarketValue());

        } catch (Exception e) {
            log.error("计算市值异常，产品代码：{}，异常信息：{}", balanceBean.getProductCode(), e.getMessage(), e);
            setDefaultMarketValue(balanceBean);
        }
    }

    /**
     * @description: 计算股权产品市值
     * @param balanceBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private void calculateEquityMarketValue(BalanceInfoBean balanceBean) {
        // 股权产品市值=净购买金额（投资成本）
        BigDecimal marketValue = balanceBean.getNetBuyAmount();
        if (marketValue == null) {
            marketValue = BigDecimal.ZERO;
        }

        balanceBean.setMarketValue(marketValue);
        balanceBean.setCurrencyMarketValue(balanceBean.getCurrencyNetBuyAmount());

        log.debug("股权产品市值计算完成，产品代码：{}，市值：{}", balanceBean.getProductCode(), marketValue);
    }

    /**
     * @description: 计算份额收益类产品市值
     * @param balanceBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private void calculateShareRevenueMarketValue(BalanceInfoBean balanceBean) {
        // 份额收益类产品：市值=份额×1
        BigDecimal balanceVol = balanceBean.getBalanceVol();
        if (balanceVol == null) {
            balanceVol = BigDecimal.ZERO;
        }

        BigDecimal marketValue = balanceVol.multiply(BigDecimal.ONE);
        balanceBean.setMarketValue(MoneyUtil.formatMoney(marketValue, 2));
        balanceBean.setCurrencyMarketValue(MoneyUtil.formatMoney(marketValue, 2));

        log.debug("份额收益类产品市值计算完成，产品代码：{}，市值：{}", balanceBean.getProductCode(), marketValue);
    }

    /**
     * @description: 计算净值类产品市值
     * @param balanceBean 持仓信息Bean
     * @param navBean 净值信息
     * @param context 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private void calculateNavBasedMarketValue(BalanceInfoBean balanceBean, HighProductNavBean navBean, BalanceQueryContext context) {
        BigDecimal nav = navBean.getNav();
        String navDate = navBean.getNavDate();
        
        if (nav == null) {
            log.warn("净值为空，产品代码：{}", balanceBean.getProductCode());
            setDefaultMarketValue(balanceBean);
            return;
        }

        // 查询最新确认订单
        List<HighDealOrderDtlLatestAckVo> latestAckList = getLatestAckOrders(balanceBean.getProductCode(), context);

        // 根据是否有最新确认订单选择计算方式
        if (CollectionUtils.isEmpty(latestAckList) || StringUtils.isEmpty(navDate)) {
            // 简单计算：市值=份额×净值
            calculateSimpleMarketValue(balanceBean, nav);
        } else {
            // 复杂计算：考虑最新确认订单
            calculateComplexMarketValue(balanceBean, nav, navDate, latestAckList);
        }
    }

    /**
     * @description: 简单市值计算
     * @param balanceBean 持仓信息Bean
     * @param nav 净值
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private void calculateSimpleMarketValue(BalanceInfoBean balanceBean, BigDecimal nav) {
        BigDecimal balanceVol = balanceBean.getBalanceVol();
        if (balanceVol == null) {
            balanceVol = BigDecimal.ZERO;
        }

        BigDecimal marketValue = balanceVol.multiply(nav);
        balanceBean.setMarketValue(MoneyUtil.formatMoney(marketValue, 2));
        balanceBean.setCurrencyMarketValue(MoneyUtil.formatMoney(marketValue, 2));

        log.debug("简单市值计算完成，产品代码：{}，份额：{}，净值：{}，市值：{}", 
                balanceBean.getProductCode(), balanceVol, nav, marketValue);
    }

    /**
     * @description: 复杂市值计算（考虑最新确认订单）
     * @param balanceBean 持仓信息Bean
     * @param nav 净值
     * @param navDate 净值日期
     * @param latestAckList 最新确认订单列表
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private void calculateComplexMarketValue(BalanceInfoBean balanceBean, BigDecimal nav, String navDate, 
                                           List<HighDealOrderDtlLatestAckVo> latestAckList) {
        // 市值=确认金额（不含费）+{持有份额-确认份额}*最新净值
        BigDecimal ackNetAmt = BigDecimal.ZERO;
        BigDecimal ackVol = BigDecimal.ZERO;

        for (HighDealOrderDtlLatestAckVo vo : latestAckList) {
            // 净值日期小于等于上报日期，市值=确认金额（不含费）+{持有份额-确认份额}*最新净值
            if (navDate.compareTo(vo.getSubmitTaDt()) <= 0) {
                ackNetAmt = ackNetAmt.add(vo.getAckAmt().subtract(vo.getFee()));
                ackVol = ackVol.add(vo.getAckVol());
            }
        }

        BigDecimal balanceVol = balanceBean.getBalanceVol();
        if (balanceVol == null) {
            balanceVol = BigDecimal.ZERO;
        }

        BigDecimal marketValue = ackNetAmt.add(balanceVol.subtract(ackVol).multiply(nav));
        balanceBean.setMarketValue(MoneyUtil.formatMoney(marketValue, 2));
        balanceBean.setCurrencyMarketValue(MoneyUtil.formatMoney(marketValue, 2));

        log.debug("复杂市值计算完成，产品代码：{}，确认金额：{}，确认份额：{}，持有份额：{}，净值：{}，市值：{}", 
                balanceBean.getProductCode(), ackNetAmt, ackVol, balanceVol, nav, marketValue);
    }

    /**
     * @description: 设置默认市值
     * @param balanceBean 持仓信息Bean
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private void setDefaultMarketValue(BalanceInfoBean balanceBean) {
        balanceBean.setMarketValue(BigDecimal.ZERO);
        balanceBean.setCurrencyMarketValue(BigDecimal.ZERO);
    }

    /**
     * @description: 设置净值信息
     * @param balanceBean 持仓信息Bean
     * @param navBean 净值信息
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private void setNavInfo(BalanceInfoBean balanceBean, HighProductNavBean navBean) {
        balanceBean.setNav(navBean.getNav());
        balanceBean.setNavDt(navBean.getNavDate());
        
        // 设置七日年化收益和万份收益（现金管理类产品）
        if (navBean.getYieldIncome() != null) {
            balanceBean.setYieldIncome(navBean.getYieldIncome());
            balanceBean.setYieldIncomeDt(navBean.getYieldIncomeDt());
        }
        if (navBean.getCopiesIncome() != null) {
            balanceBean.setCopiesIncome(navBean.getCopiesIncome());
        }
    }

    /**
     * @description: 判断是否为固收份额收益类产品
     * @param balanceBean 持仓信息Bean
     * @return boolean 是否为固收份额收益类产品
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private boolean isFixedIncomeShareRevenueProduct(BalanceInfoBean balanceBean) {
        return ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(balanceBean.getProductSubType())
                && NavDisclosureTypeEnum.FESY.getCode().equals(balanceBean.getNavDisclosureType());
    }

    /**
     * @description: 获取净值信息
     * @param productCode 产品代码
     * @param context 查询上下文
     * @return HighProductNavBean 净值信息
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private HighProductNavBean getNavInfo(String productCode, BalanceQueryContext context) {
        // TODO: 从上下文缓存中获取净值信息，如果没有则查询
        // 这里需要实现具体的净值查询逻辑
        return null;
    }

    /**
     * @description: 获取最新确认订单
     * @param productCode 产品代码
     * @param context 查询上下文
     * @return List<HighDealOrderDtlLatestAckVo> 最新确认订单列表
     * @author: hongdong.xie
     * @date: 2025/8/26 15:20
     * @since JDK 1.8
     */
    private List<HighDealOrderDtlLatestAckVo> getLatestAckOrders(String productCode, BalanceQueryContext context) {
        // TODO: 查询最新确认订单
        // 这里需要实现具体的订单查询逻辑
        return null;
    }
}
