package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.constant.MessageSource;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.QueryAcctBalanceRefactorFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.QueryAcctBalanceRefactorRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.QueryAcctBalanceRefactorResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.BalanceInfoBean;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor.BalanceDataProcessor;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor.BalanceSummaryProcessor;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor.ConsignmentBalanceProcessor;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.processor.DirectBalanceProcessor;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancerefactor.context.BalanceQueryContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 查询客户持仓信息实现（重构版）
 * @author: hongdong.xie
 * @date: 2025/8/26 14:30
 * @since JDK 1.8
 */
@Slf4j
@DubboService
@Component
public class QueryAcctBalanceRefactorFacadeService implements QueryAcctBalanceRefactorFacade {

    @Autowired
    private ConsignmentBalanceProcessor consignmentBalanceProcessor;

    @Autowired
    private DirectBalanceProcessor directBalanceProcessor;

    @Autowired
    private BalanceDataProcessor balanceDataProcessor;

    @Autowired
    private BalanceSummaryProcessor balanceSummaryProcessor;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.QueryAcctBalanceRefactorFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryAcctBalanceRefactorFacadeService
     * @apiName execute
     * @apiDescription 查询产品持仓接口实现（重构版）- 整个高端持仓基础接口
     * @apiParam (请求参数) {String} hkSaleFlag 好买香港代销标识 0-否，1-是
     * @apiParam (请求参数) {String} productCode 产品代码
     * @apiParam (请求参数) {String} productType 产品类型
     * @apiParam (请求参数) {String} productSubType 产品子类型 1-股权，2-固定收益，3-现金管理等
     * @apiParam (请求参数) {String} protocolType 协议类型，4-高端产品协议
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} callType 调用类型 1-新资产中心，2-老资产中心
     * @apiParam (请求参数) {String} balanceStatus 持仓状态 0-不持仓，1-持仓，2-全部，默认查持仓
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品 1-是，0-否
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品 1-是，0-否
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期 格式：YYYYMMDD
     * @apiParam (请求参数) {String} appTm 申请时间 格式：HHMMSS
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParamExample 请求参数示例
     * {"hkSaleFlag":"0","productCode":"","productType":"","productSubType":"","protocolType":"4","disCodeList":["HM"],"callType":"2","balanceStatus":"1","notFilterHkFund":"0","notFilterHzFund":"0","txAcctNo":"123456789","hbOneNo":"HB123456","disCode":"HM","outletCode":"001","appDt":"20250826","appTm":"143000","operIp":"***********","txCode":"HIGH_FUND_QUERY_ACCT_BALANCE","txChannel":"2"}
     * @apiSuccess (响应结果) {String} txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} disCode 分销机构代码
     * @apiSuccess (响应结果) {Array} disCodeList 分销机构代码列表
     * @apiSuccess (响应结果) {Number} totalMarketValue 总市值（人民币）
     * @apiSuccess (响应结果) {Number} totalUnconfirmedAmt 总在途金额（人民币）
     * @apiSuccess (响应结果) {Number} totalCurrentAsset 当前总收益（人民币）
     * @apiSuccess (响应结果) {String} totalIncomCalStat 总收益计算状态 0-计算中，1-计算成功
     * @apiSuccess (响应结果) {Array} balanceList 持仓信息列表
     * @apiSuccess (响应结果) {String} balanceList.productCode 产品代码
     * @apiSuccess (响应结果) {String} balanceList.productName 产品名称
     * @apiSuccess (响应结果) {Number} balanceList.balanceVol 持仓份额
     * @apiSuccess (响应结果) {Number} balanceList.marketValue 市值（人民币）
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccessExample 响应结果示例
     * {"txAcctNo":"123456789","disCode":"HM","disCodeList":["HM"],"totalMarketValue":"100000.00","totalUnconfirmedAmt":"5000.00","totalCurrentAsset":"10000.00","totalIncomCalStat":"1","balanceList":[{"productCode":"HM001","productName":"好买基金1号","balanceVol":"10000.00","marketValue":"10500.00"}],"returnCode":"0000","description":"成功"}
     */
    @Override
    public QueryAcctBalanceRefactorResponse execute(QueryAcctBalanceRefactorRequest request) {
        log.info("查询客户持仓信息开始（重构版），请求参数：{}", JSON.toJSONString(request));

        try {
            // 1. 构建查询上下文
            BalanceQueryContext context = buildQueryContext(request);

            // 2. 初始化响应对象
            QueryAcctBalanceRefactorResponse response = initResponse(request);

            // 3. 参数校验
            if (!validateRequest(context)) {
                log.warn("参数校验失败，txAcctNo和hbOneNo都为空");
                return response;
            }

            // 4. 查询代销持仓
            List<BalanceInfoBean> consignmentBalanceList = consignmentBalanceProcessor.processConsignmentBalance(context);

            // 5. 查询直销持仓
            List<BalanceInfoBean> directBalanceList = directBalanceProcessor.processDirectBalance(context);

            // 6. 合并持仓数据
            List<BalanceInfoBean> allBalanceList = mergeBalanceList(consignmentBalanceList, directBalanceList);

            // 7. 数据处理（过滤、配置、特殊字段等）
            balanceDataProcessor.processBalanceData(allBalanceList, context);

            // 8. 资产汇总
            balanceSummaryProcessor.processSummary(response, allBalanceList, context);

            // 9. 设置响应结果
            response.setBalanceList(allBalanceList);

            log.info("查询客户持仓信息成功（重构版），返回{}条持仓记录", allBalanceList.size());
            return response;

        } catch (Exception e) {
            log.error("查询客户持仓信息异常（重构版）：{}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * @description: 构建查询上下文
     * @param request 请求对象
     * @return BalanceQueryContext 查询上下文
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private BalanceQueryContext buildQueryContext(QueryAcctBalanceRefactorRequest request) {
        BalanceQueryContext context = new BalanceQueryContext();
        context.setRequest(request);
        context.setTxAcctNo(request.getTxAcctNo());
        context.setHbOneNo(request.getHbOneNo());
        context.setDisCodeList(getDisCodeList(request));
        context.setProductCode(request.getProductCode());
        context.setBalanceStatus(request.getBalanceStatus());
        context.setCallType(request.getCallType());
        return context;
    }

    /**
     * @description: 初始化响应对象
     * @param request 请求对象
     * @return QueryAcctBalanceRefactorResponse 响应对象
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private QueryAcctBalanceRefactorResponse initResponse(QueryAcctBalanceRefactorRequest request) {
        QueryAcctBalanceRefactorResponse response = new QueryAcctBalanceRefactorResponse();
        response.setTxAcctNo(request.getTxAcctNo());
        response.setDisCode(request.getDisCode());
        response.setDisCodeList(getDisCodeList(request));
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        return response;
    }

    /**
     * @description: 参数校验
     * @param context 查询上下文
     * @return boolean 校验结果
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private boolean validateRequest(BalanceQueryContext context) {
        return !(context.getTxAcctNo() == null && context.getHbOneNo() == null);
    }

    /**
     * @description: 获取分销机构代码列表
     * @param request 请求对象
     * @return List<String> 分销机构代码列表
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private List<String> getDisCodeList(QueryAcctBalanceRefactorRequest request) {
        if (!CollectionUtils.isEmpty(request.getDisCodeList())) {
            return request.getDisCodeList();
        }
        List<String> disCodeList = new ArrayList<>();
        disCodeList.add(request.getDisCode());
        return disCodeList;
    }

    /**
     * @description: 合并持仓列表
     * @param consignmentList 代销持仓列表
     * @param directList 直销持仓列表
     * @return List<BalanceInfoBean> 合并后的持仓列表
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    private List<BalanceInfoBean> mergeBalanceList(List<BalanceInfoBean> consignmentList, List<BalanceInfoBean> directList) {
        List<BalanceInfoBean> allBalanceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(consignmentList)) {
            allBalanceList.addAll(consignmentList);
        }
        if (!CollectionUtils.isEmpty(directList)) {
            allBalanceList.addAll(directList);
        }
        return allBalanceList;
    }
}
