/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryfinreceipt;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.outerservice.paramcenter.queryalltafacade.QueryAllTaService;
import com.howbuy.tms.common.outerservice.paramcenter.queryfundman.QueryFundManService;
import com.howbuy.tms.high.orders.dao.vo.DealOrderVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptFacade;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptRequest;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptResponse;
import com.howbuy.tms.high.orders.service.repository.DealOrderRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryBalanceParam;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.RefundDealOrderInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description:查询在途笔数、资金到账提醒数据接口实现
 * @reason:
 * @date 202年1月10日 下午8:24:12
 * @since JDK 1.8
 */
@DubboService
@Service("queryFinReceiptFacade")
public class QueryFinReceiptFacadeService implements QueryFinReceiptFacade {

    private static final Logger logger = LogManager.getLogger(QueryFinReceiptFacadeService.class);

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Autowired
    private DealOrderRepository dealOrderRepository;

    @Autowired
    private TradeDayService tradeDayService;

    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private QueryFundManService queryFundManService;

    @Autowired
    private QueryAllTaService queryAllTaService;

    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;


    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptFacade.execute(QueryFinReceiptRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryFinReceiptFacadeService
     * @apiName execute
     * @apiDescription 查询在途笔数、资金到账提醒数据接口实现
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} onlyHkProduct 是否仅需香港产品
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:是,0:否
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:是,0:否
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * notFilterHzFund=a251NH&hbOneNo=XQ&pageSize=8517&disCode=Utv&txChannel=yNRoFsZ&appTm=fNn&disCodeList=mJTC&subOutletCode=ZhL4Lx&pageNo=4988&operIp=jvw6EpT&txAcctNo=bTU1hPz&onlyHkProduct=s&appDt=mMo1KAYe&dataTrack=4eYdaJ3l0&notFilterHkFund=Pmze&txCode=kwt&outletCode=5O
     * @apiSuccess (响应结果) {String} txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} hbOneNo 一账通账号
     * @apiSuccess (响应结果) {Number} buyUnrefundedPiece 购买待退款订单数
     * @apiSuccess (响应结果) {Number} redeemUnrefundedPiece 赎回待回款订单数
     * @apiSuccess (响应结果) {Array} unpaidList 待付款订单
     * @apiSuccess (响应结果) {String} unpaidList.dealNo 订单号
     * @apiSuccess (响应结果) {String} unpaidList.dealType 订单类型：0-直销、1-代销
     * @apiSuccess (响应结果) {Array} unconfirmedList 待确认订单
     * @apiSuccess (响应结果) {String} unconfirmedList.dealNo 订单号
     * @apiSuccess (响应结果) {String} unconfirmedList.dealType 订单类型：0-直销、1-代销
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"p","hbOneNo":"R7WZ","totalPage":3439,"pageNo":7413,"txAcctNo":"fFmMkOStY","unpaidList":[{"dealNo":"uMUAcfFy","dealType":"i"}],"description":"PZS","buyUnrefundedPiece":4768,"redeemUnrefundedPiece":5889,"totalCount":9102,"unconfirmedList":[{"dealNo":"gskzZG","dealType":"lGxcn1y"}]}
     */
    @Override
    public QueryFinReceiptResponse execute(QueryFinReceiptRequest request) {
        if (StringUtils.isNotBlank(request.getIsAuth()) && YesOrNoEnum.NO.getCode().equals(request.getIsAuth())) {
            request.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            request.setNotFilterHzFund(YesOrNoEnum.NO.getCode());
        } else {
            if (StringUtils.isBlank(request.getNotFilterHkFund())) {
                request.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            }
            if (StringUtils.isBlank(request.getNotFilterHzFund())) {
                request.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            }
        }
        QueryFinReceiptResponse response = new QueryFinReceiptResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        String txAcctNo = request.getTxAcctNo();
        String hbOneNo = request.getHbOneNo();
        List<String> disCodeList = request.getDisCodeList();
        if (StringUtils.isEmpty(txAcctNo)) {
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
        }
        if (StringUtils.isEmpty(txAcctNo)) {
            logger.error("QueryFinReceiptResponse|execute() txAcctNo is null, hbOneNo:{}", hbOneNo);
            response.setReturnCode(ExceptionCodes.ORDER_CENTER_TXCODE_IS_NULL_ERROR);
            response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.ORDER_CENTER_TXCODE_IS_NULL_ERROR));
            return response;
        }

        response.setTxAcctNo(txAcctNo);
        response.setHbOneNo(hbOneNo);
        // 获取资金提醒笔数
        QueryBalanceParam queryBalanceParam = new QueryBalanceParam();
        queryBalanceParam.setTxAcctNo(txAcctNo);
        if (request.getNotFilterHzFund() != null && YesOrNoEnum.NO.getCode().equals(request.getNotFilterHzFund())) {
            disCodeList = disCodeList.stream().filter(x -> !DisCodeEnum.HZ.getCode().equals(x)).collect(Collectors.toList());
        }
        queryBalanceParam.setDisCodeList(disCodeList);
        List<RefundDealOrderInfo> refundDealOrderInfos = acctBalanceBaseInfoService.queryRefundAmtDealOrderInfo(queryBalanceParam);
        List<RefundDealOrderInfo> buyRefundList = refundDealOrderInfos.stream().filter(x -> YesOrNoEnum.YES.getCode().equals(x.getIsBuyFund())).collect(Collectors.toList());
        List<RefundDealOrderInfo> redeemRefundList = refundDealOrderInfos.stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getIsBuyFund())).collect(Collectors.toList());
        response.setBuyUnrefundedPiece(buyRefundList.size());
        response.setRedeemUnrefundedPiece(redeemRefundList.size());
        // 在途笔数
        getOnWay(response, txAcctNo, disCodeList, request.getNotFilterHkFund(), request.getNotFilterHzFund(), request.getOnlyHkProduct());

        return response;
    }


    /**
     * 获取在途笔数
     *
     * @param response
     * @param txAcctNo
     * @param disCodeList
     */
    private void getOnWay(QueryFinReceiptResponse response, String txAcctNo, List<String> disCodeList, String notFilterHkFund, String notFilterHzFund, String onlyHkProduct) {
        // 待付款订单
        List<QueryAcctBalanceResponse.DealOrderBean> unpaidList = new ArrayList<>();
        // 待确认订单
        List<QueryAcctBalanceResponse.DealOrderBean> unconfirmedList = new ArrayList<>();

        // 待付款订单
        List<DealOrderVo> dbUnpaidList = dealOrderRepository.selectUnpaid(disCodeList, txAcctNo, notFilterHkFund, notFilterHzFund, onlyHkProduct);
        if (CollectionUtil.isNotEmpty(dbUnpaidList)) {
            dbUnpaidList = excludeMergeSubmit(dbUnpaidList);
            unpaidList = JSON.parseArray(JSON.toJSONString(dbUnpaidList), QueryAcctBalanceResponse.DealOrderBean.class);
        }
        // 待确认订单数-代销unconfirmedList
        List<DealOrderVo> dbUnconfirmedList = dealOrderRepository.selectUnconfirmed(disCodeList, txAcctNo, notFilterHkFund, notFilterHzFund, onlyHkProduct);
        if (CollectionUtil.isNotEmpty(dbUnconfirmedList)) {
            dbUnconfirmedList = excludeMergeSubmit(dbUnconfirmedList);
            unconfirmedList = JSON.parseArray(JSON.toJSONString(dbUnconfirmedList), QueryAcctBalanceResponse.DealOrderBean.class);
        }
        // 待付款订单
        response.setUnpaidList(unpaidList);
        // 待确认订单
        response.setUnconfirmedList(unconfirmedList);
    }


    /**
     * 非合并上报的订单，按照订单维度统计返回笔数。合并上报的订单，按照主订单维度统计返回笔数
     *
     * @param list
     */
    private List<DealOrderVo> excludeMergeSubmit(List<DealOrderVo> list) {
        Set<String> consignmentBuyRefundSet = new HashSet<>();
        List<DealOrderVo> returnList = new ArrayList<>();
        for (DealOrderVo vo : list) {
            if (YesOrNoEnum.YES.getCode().equals(vo.getMergeSubmitFlag())) {
                if (!consignmentBuyRefundSet.contains(vo.getMainDealOrderNo())) {
                    consignmentBuyRefundSet.add(vo.getMainDealOrderNo());
                    returnList.add(vo);
                }
            } else {
                returnList.add(vo);
            }
        }
        return returnList;
    }
}
