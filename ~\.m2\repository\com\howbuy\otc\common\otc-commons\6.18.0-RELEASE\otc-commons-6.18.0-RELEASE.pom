<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.howbuy.otc.common</groupId>
  <artifactId>otc-commons</artifactId>
  <version>6.18.0-RELEASE</version>
  <packaging>pom</packaging>
  
  	<properties>
		<project.reporting.outputEncoding>utf-8</project.reporting.outputEncoding>
		<com.howbuy.otc-commons.version>6.18.0-RELEASE</com.howbuy.otc-commons.version>
		<com.howbuy.otc-common.version>6.18.0-RELEASE</com.howbuy.otc-common.version>
</properties>

	<repositories>
		<repository>
			<id>maven2-repository.dev.java.net</id>
			<name>Java.net Repository for Maven</name>
			<url>http://download.java.net/maven/2/</url>
			<layout>default</layout>
		</repository>
		<repository>
			<id>ReleaseRepo</id>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/thirdparty</url>
		</repository>
		<repository>
			<id>nexus</id>
			<name>local private nexus</name>
			<url>http://maven.oschina.net/content/groups/public/</url>
			<releases>
			</releases>
			<snapshots>
			</snapshots>
		</repository>
	</repositories>

	<distributionManagement>
		<repository>
			<id>howbuy-releases</id>
			<name>Nexus Releases Repository</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshots</id>
			<name>Nexus Snapshots Repository</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots</url>
		</snapshotRepository>
	</distributionManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF8</encoding> 
				</configuration>
			</plugin>
		</plugins>
	</build>
	
	<modules>
		<module>otc-commons-utils</module>
		<module>otc-commons-server</module>
		<module>otc-commons-client</module>
	</modules>
</project>