# ✅ AI MQ消息处理类代码生成提示词（基于详细设计文档）
你是一名资深后端开发工程师，请根据我提供的**MQ消息处理详细设计文档**内容，生成符合当前项目标准的**MQ消息处理代码实现**，输出要求如下：

## 📌 输出要求：
- **输出格式**：Java 源代码，分模块输出，可直接粘贴到项目中使用  
- **遵循规范**：必须严格遵循项目规范，规则文件路径为 `.cursor/rules/`
- **避免重复造轮子**：请优先使用项目中已有的工具类、封装模块、基础框架，不得重复实现已有功能
- **消息处理架构**：基于RocketMQ的消息处理框架，支持普通消息、Tag消息和特殊格式消息处理
- **清晰注释**：对关键逻辑、业务处理、异常情况、数据转换进行清晰注释
- **可维护性**：结构清晰、职责单一、易测试易扩展，体现生产级代码风格

## 📘 输入内容包含：
由我提供的**MQ消息处理详细设计文档**，结构如下：
- **功能名称**：消息处理器的功能描述
- **消息类型**：普通消息/Tag消息/香港账户中心Tag消息
- **Topic配置**：消息队列Topic名称和配置Key
- **Tag配置**（如适用）：消息Tag名称和配置Key
- **消息格式**：消息体结构、字段说明、示例数据
- **业务处理流程**：包括消息解析、数据校验、业务逻辑、调用链
- **调用依赖模块**：例如Service、Repository、外部接口等
- **异常处理策略**：包括业务异常、告警机制、失败处理等
- **并发控制**（如需要）：分布式锁、幂等处理等

## 🧠 你的实现任务：
### 1. **消息处理器主类**
根据消息类型生成对应的处理器类：

#### 普通消息处理器
```java
@Slf4j
@Component
public class ExampleMessageProcessor extends AbstractMessageProcessor<ExampleMessageDTO> {
    @Value("${TOPIC.EXAMPLE_MESSAGE}")
    private String topic;
    
    @Override
    protected String getTopicName() {
        return topic;
    }
    
    @Override
    protected void handleMessage(ExampleMessageDTO message) {
        // 具体业务逻辑
    }
}
```

#### Tag消息处理器
```java
@Slf4j
@Component
public class ExampleTagMessageProcessor extends AbstractTagMessageProcessor<ExampleMessageDTO> {
    @Value("${TOPIC.EXAMPLE_MESSAGE}")
    private String topic;
    
    @Value("${TAG.EXAMPLE_MESSAGE}")
    private String tag;
    
    @Override
    protected String getTopicName() {
        return topic;
    }
    
    @Override
    protected String getTag() {
        return tag;
    }
    
    @Override
    protected void handleMessage(ExampleMessageDTO message) {
        // 具体业务逻辑
    }
}
```

#### 香港账户中心Tag消息处理器
```java
@Slf4j
@Component
public class ExampleHkMessageProcessor extends AbstractHkMessageProcessor<ExampleMessageDTO> {
    @Value("${TOPIC.EXAMPLE_MESSAGE}")
    private String topic;
    
    @Value("${TAG.EXAMPLE_MESSAGE}")
    private String tag;
    
    @Override
    protected String getTopicName() {
        return topic;
    }
    
    @Override
    protected String getTag() {
        return tag;
    }
    
    @Override
    protected void handleMessage(ExampleMessageDTO message) {
        // 具体业务逻辑
    }
}
```

### 2. **消息DTO类**
生成消息数据传输对象：
```java
@Getter
@Setter
public class ExampleMessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 业务字段说明
     */
    private String businessField;
}
```

### 3. **业务处理逻辑**
根据设计文档生成具体的业务处理代码：
- 注入所需的Service、Repository等依赖
- 实现消息数据校验和转换
- 实现具体的业务逻辑处理
- 如需要分布式锁，使用`LockService`进行并发控制
- 合理的日志记录和性能监控

### 4. **配置参数**
生成相关的配置参数定义：
- Topic名称配置
- Tag名称配置（如适用）
- 其他业务相关配置

## 📋 项目MQ消息处理架构规范：
### 基础架构选择
```java
// 1. 普通消息处理器 - 继承AbstractMessageProcessor
public class ExampleProcessor extends AbstractMessageProcessor<ExampleDTO> {
}

// 2. Tag消息处理器 - 继承AbstractTagMessageProcessor  
public class ExampleTagProcessor extends AbstractTagMessageProcessor<ExampleDTO> {
}

// 3. 香港账户中心Tag消息处理器 - 继承AbstractHkMessageProcessor
// 特殊消息格式：{"body": "{}", "head": {"tag": "TAG_NAME"}}
public class ExampleHkProcessor extends AbstractHkMessageProcessor<ExampleDTO> {
}
```

### 消息DTO规范
```java
// 消息DTO实现Serializable接口
@Getter
@Setter
public class ExampleMessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 字段注释说明
     */
    private String fieldName;
}
```

### 标准版权头
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
```

### 方法注释规范
```java
/**
 * @description: 方法功能描述
 * @param paramName 参数说明
 * @return 返回值说明
 * @author: hongdong.xie
 * @date: 2025-07-02 16:48:47
 * @since JDK 1.8
 */
```

### 业务处理规范
```java
@Override
protected void handleMessage(ExampleMessageDTO message) {
    log.info("消息处理开始，消息内容：{}", JSON.toJSONString(message));
    try {
        // 1. 参数校验
        AssertUtils.nonNullParam(message.getBusinessField(), "业务字段不能为空");
        
        // 2. 业务逻辑处理
        businessService.process(message);
        
        log.info("消息处理成功");
    } catch (Exception e) {
        log.error("消息处理失败：{}", e.getMessage(), e);
        // 框架会自动处理异常告警
        throw e;
    }
}
```

### 分布式锁处理规范（如需要）
```java
@Resource
private LockService lockService;

private static final int DEFAULT_EXPIRE = 600;

@Override
protected void handleMessage(ExampleMessageDTO message) {
    String lockKey = CacheKeyPrefix.LOCK_KEY_PREFIX + "business_" + message.getBusinessId();
    if (!lockService.getLock(lockKey, DEFAULT_EXPIRE)) {
        log.info("获取锁失败，跳过处理：{}", lockKey);
        return;
    }
    
    try {
        // 业务处理逻辑
        processBusinessLogic(message);
    } finally {
        lockService.releaseLock(lockKey);
    }
}
```

## 📂 文件存放位置规范：
### 消息处理器存放位置
- **普通消息处理器**：`dtms-order-service/src/main/java/com/howbuy/dtms/order/service/mq/`
- **业务分类子包**：根据业务功能划分，如`order/`、`acc/`、`batch/`等
- **特殊处理器**：`dtms-order-service/src/main/java/com/howbuy/dtms/order/service/mq/acc/`（香港账户中心）

### 消息DTO存放位置
- **消息DTO**：`dtms-order-service/src/main/java/com/howbuy/dtms/order/service/mq/domain/`
- **业务分类子包**：与处理器的子包结构保持一致

### 命名规范
- **消息处理器类**：以 `MessageProcessor` 或 `Processor` 结尾，如 `OrderCreateMessageProcessor`、`UpdateShareLockEndDtProcessor`
- **消息DTO类**：以 `MessageDTO` 或 `DTO` 结尾，如 `OrderCreateMessageDTO`、`UpdateShareLockEndDtDTO`

## 🚫 禁止事项：
| 类型 | 说明 |
|------|------|
| ❌ 重复造轮子 | 必须使用项目现有的消息处理框架 |
| ❌ 硬编码配置 | Topic和Tag名称必须通过`@Value`从配置文件获取 |
| ❌ 违反目录结构 | 所有类必须放在指定的包路径下 |
| ❌ 缺少异常处理 | 框架提供统一异常处理，但业务异常需要合理抛出 |
| ❌ 不规范注释 | 必须使用标准的版权头和方法注释 |
| ❌ 选错基础类 | 必须根据消息类型选择正确的基础处理器 |
| ❌ 禁止使用魔法值 | 任何未经定义的字面量都应定义为常量或枚举 |
| ❌ 缺少枚举或常量 | 对于状态、类型等字段，必须创建对应的枚举类或常量 |

## 🎯 自动支持的功能：
通过继承对应的基础消息处理器，你的消息处理器将自动获得：
- ✅ **链路追踪**：自动生成traceId用于日志追踪
- ✅ **消息解析**：自动将消息解析为指定的DTO对象
- ✅ **异常处理**：统一的异常捕获和告警机制
- ✅ **日志记录**：标准的消息接收和处理日志
- ✅ **Spring集成**：自动注册到消息处理服务
- ✅ **类型安全**：基于泛型的类型安全保障

## 📋 消息类型选择指南：
### 普通消息（AbstractMessageProcessor）
- **使用场景**：简单的消息处理，不需要Tag分类
- **消息格式**：直接的JSON字符串或对象
- **适用业务**：订单处理、状态更新等基础业务

### Tag消息（AbstractTagMessageProcessor）  
- **使用场景**：需要根据Tag进行消息分类处理
- **消息格式**：带有Tag标识的标准RocketMQ消息
- **适用业务**：需要按业务类型分类的复杂消息处理

### 香港账户中心Tag消息（AbstractHkMessageProcessor）
- **使用场景**：香港账户中心系统的特殊消息格式
- **消息格式**：`{"body": "{具体业务数据}", "head": {"tag": "标签", "其他头信息": "值"}}`
- **适用业务**：账户开户、账户信息更新等香港账户相关业务

## 📎 提示词使用方式：
在我提供详细的MQ消息处理设计文档后，请立即生成符合上述规范的完整Java实现代码，包括：
1. 消息处理器主类（继承正确的基础类）
2. 消息DTO类（实现Serializable接口）
3. 相关的业务处理逻辑
4. 必要的配置参数定义
5. 如需要，提供分布式锁控制代码

确保代码结构清晰、注释完整、符合项目规范，可以直接在项目中使用。 