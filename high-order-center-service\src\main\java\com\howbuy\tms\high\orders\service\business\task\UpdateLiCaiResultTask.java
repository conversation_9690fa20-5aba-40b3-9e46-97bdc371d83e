package com.howbuy.tms.high.orders.service.business.task;

import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.po.UserShowResultInfoPo;
import com.howbuy.tms.high.orders.service.business.queryneedshowlicai.QueryNeedShowLiCaiServiceImpl;
import com.howbuy.tms.high.orders.service.repository.UserShowResultInfoRepository;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @Description:更新理财结果
 * @Author: yun.lu
 * Date: 2024/12/9 20:16
 */
@Data
public class UpdateLiCaiResultTask extends HowbuyBaseTask {
    /**
     * 交易账号
     */
    private List<String> txAcctNoList;
    /**
     * 一账通
     */
    private List<String> hbOneNoList;
    /**
     * 计算逻辑
     */
    private QueryNeedShowLiCaiServiceImpl queryNeedShowLiCaiService;
    private UserShowResultInfoRepository userShowResultInfoRepository;

    @Override
    protected void callTask() {
        if (CollectionUtils.isNotEmpty(txAcctNoList)) {
            for (String txAcctNo : txAcctNoList) {
                // 1.计算最新结果
                UserShowResultInfoPo needShowLiCai = queryNeedShowLiCaiService.getNeedShowLiCai(null, txAcctNo);
                if (needShowLiCai == null) {
                    return;
                }
                // 2.先删除
                if (StringUtils.isNotBlank(txAcctNo)) {
                    userShowResultInfoRepository.deleteByTxAcctNo(txAcctNo);
                }
                // 3.再新增
                userShowResultInfoRepository.add(needShowLiCai);
            }
        }
        if (CollectionUtils.isNotEmpty(hbOneNoList)) {
            for (String hbOneNo : hbOneNoList) {
                // 1.计算最新结果
                UserShowResultInfoPo needShowLiCai = queryNeedShowLiCaiService.getNeedShowLiCai(hbOneNo, null);
                if (needShowLiCai == null) {
                    return;
                }
                // 2.先删除
                if (StringUtils.isNotBlank(hbOneNo)) {
                    userShowResultInfoRepository.deleteByHbOneNo(hbOneNo);
                }
                // 3.再新增
                userShowResultInfoRepository.add(needShowLiCai);
            }
        }


    }
}
