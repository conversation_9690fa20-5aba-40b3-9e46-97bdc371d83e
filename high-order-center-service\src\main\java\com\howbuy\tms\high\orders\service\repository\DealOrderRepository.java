package com.howbuy.tms.high.orders.service.repository;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.tms.high.orders.dao.mapper.customize.DealOrderPoMapper;
import com.howbuy.tms.high.orders.dao.po.DealOrderPo;
import com.howbuy.tms.high.orders.dao.vo.*;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.RefundDealOrderInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class DealOrderRepository {
    @Autowired
    private DealOrderPoMapper dealOrderPoMapper;

    public int updateCancelOrder(DealOrderPo dealOrderPo) {
        return dealOrderPoMapper.updateCancelOrder(dealOrderPo);
    }

    public CancelOrderVo selectFundCancelOrder(String txAcctNo, String dealNo) {
        return dealOrderPoMapper.selectFundCancelOrder(txAcctNo, dealNo);
    }

    public List<CancelOrderVo> selectFundCancelOrderSubOrders(String txAcctNo, String mainDealNo) {
        return dealOrderPoMapper.selectFundCancelOrderSubOrders(txAcctNo, mainDealNo);
    }

    public DealOrderPo selectByDealNo(String dealNo) {
        return dealOrderPoMapper.selectByDealNo(dealNo);
    }

    public Page<DealOrderVo> queryDealOrderListWithDirect(QueryDealOrderListCondition condition, Integer pageNo, Integer pageSize) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }
        return dealOrderPoMapper.queryDealOrderListWithDirect(condition);
    }

    public int countHkDealNum(QueryDealOrderListCondition condition) {
        return dealOrderPoMapper.countHkDealNum(condition);
    }

    public int countHzDealNum(QueryDealOrderListCondition condition) {
        return dealOrderPoMapper.countHzDealNum(condition);
    }

    public int selectNotCompleteBuyCount(String txAcctNo, String fundCode) {
        return dealOrderPoMapper.selectNotCompleteBuyCount(txAcctNo, fundCode);
    }

    public List<String> selectNotCompleteBuyCountMap(String txAcctNo, List<String> fundCodes) {
        return dealOrderPoMapper.selectNotCompleteBuyCountMap(txAcctNo, fundCodes);
    }

    public List<String> getNotCompleteBuyByTxAcctNoAndDisCode(String txAcctNo, String disCode) {
        return dealOrderPoMapper.getNotCompleteBuyByTxAcctNoAndDisCode(txAcctNo, disCode);
    }

    public List<DealOrderVo> selectCancelOrderList(Date currDate, String txAcctNo) {
        return dealOrderPoMapper.selectCancelOrderList(currDate, txAcctNo);
    }

    public List<DealOrderVo> selectForceCancelOrderList(String taTradeDt, String txAcctNo, String disCode) {
        return dealOrderPoMapper.selectForceCancelOrderList(taTradeDt, txAcctNo, disCode);
    }

    public List<DealOrderVo> selectMergeOrderTotalInfoList(List<String> mainDealNoList) {
        return dealOrderPoMapper.selectMergeOrderTotalInfoList(mainDealNoList);
    }

    public List<String> selectHisCpAcctNo(String txAcctNo, String disCode, String fundCode, String taCode) {
        return dealOrderPoMapper.selectHisCpAcctNo(txAcctNo, disCode, fundCode, taCode);
    }

    public List<DealOrderPo> selectrOnWayModifyDivOrderList(String txAcctNo) {
        return dealOrderPoMapper.selectrOnWayModifyDivOrderList(txAcctNo);
    }

    public List<QueryModifyDivOrdelVo> selectAckModifyDivOrderListByAckDt(String txAcctNo, String ackDt) {
        return dealOrderPoMapper.selectAckModifyDivOrderListByAckDt(txAcctNo, ackDt);
    }

    public int selectFirstBuyOrderCount(String txAcctNo, String fundCode) {
        return dealOrderPoMapper.selectFirstBuyOrderCount(txAcctNo, fundCode);
    }

    public int selectOnWayTradeNum(List<String> disCodeList, String txAcctNo, String notFilterHkFund, String notFilterHzFund) {
        return dealOrderPoMapper.selectOnWayTradeNum(disCodeList, txAcctNo, notFilterHkFund, notFilterHzFund);
    }

    public List<DealOrderVo> queryAdvanceDealOrderList(QueryDealOrderListCondition condition) {
        return dealOrderPoMapper.queryAdvanceDealOrderList(condition);
    }

    public QueryDealOrderVo queryDealOrderByDealNo(String dealNo) {
        return dealOrderPoMapper.queryDealOrderByDealNo(dealNo);
    }

    public QueryDealOrderMsgVo selectDealOrderMsgByDealNo(String dealNo) {
        return dealOrderPoMapper.selectDealOrderMsgByDealNo(dealNo);
    }

    public Integer selectRedeemOnWayTradeNum(List<String> disCodeList, String txAcctNo, String productCode) {
        return dealOrderPoMapper.selectRedeemOnWayTradeNum(disCodeList, txAcctNo, productCode);
    }

    public Integer selectRedeemOnWayTradeAuthNum(List<String> disCodeList, String txAcctNo, String notFilterHkFund, String notFilterHzFund) {
        return dealOrderPoMapper.selectRedeemOnWayTradeAuthNum(disCodeList, txAcctNo, notFilterHkFund, notFilterHzFund);
    }

    public List<String> selectBuyOnwayCpAcctNo(String txAcctNo, String fundCode) {
        return dealOrderPoMapper.selectBuyOnwayCpAcctNo(txAcctNo, fundCode);
    }

    public Page<QueryDealOrderRefundVo> queryDealOrderForRefund(QueryDealOrderRefundCondition condition, Integer pageNo, Integer pageSize) {
        if (pageNo != null && pageSize != null) {
            PageHelper.startPage(pageNo, pageSize);
        }
        return dealOrderPoMapper.queryDealOrderForRefund(condition);
    }

    public List<DealOrderVo> selectUnpaid(List<String> disCodeList, String txAcctNo, String notFilterHkFund, String notFilterHzFund, String onlyHkProduct) {
        return dealOrderPoMapper.selectUnpaid(disCodeList, txAcctNo, notFilterHkFund, notFilterHzFund, onlyHkProduct);
    }

    public List<DealOrderVo> selectUnconfirmed(List<String> disCodeList, String txAcctNo, String notFilterHkFund, String notFilterHzFund, String onlyHkProduct) {
        return dealOrderPoMapper.selectUnconfirmed(disCodeList, txAcctNo, notFilterHkFund, notFilterHzFund, onlyHkProduct);
    }

    public List<RefundDealOrderVo> selectDirectRedeemReturn(String txAcctNo, String tradedt, String dataQueryType) {
        return dealOrderPoMapper.selectDirectRedeemReturn( txAcctNo, tradedt, dataQueryType);
    }

    public List<RefundDealOrderVo> selectConsignmentBuyRefund(String txAcctNo, String refundDt, String payOutDt, String ackDt, String dataQueryType) {
        return dealOrderPoMapper.selectConsignmentBuyRefund(txAcctNo, refundDt, payOutDt, ackDt, dataQueryType);
    }

    public List<RefundDealOrderVo> selectConsignmentRedeemReturnNotPay( String txAcctNo, String ackDt,  String dataQueryType) {
        return dealOrderPoMapper.selectConsignmentRedeemReturnNotPay( txAcctNo, ackDt, dataQueryType);
    }

    public List<RefundDealOrderVo> selectConsignmentRedeemReturnPay( String txAcctNo, String payOutDt, String ackDt, String dataQueryType) {
        return dealOrderPoMapper.selectConsignmentRedeemReturnPay( txAcctNo, payOutDt, ackDt, dataQueryType);
    }

    public DealOrderPo getByExternalDealNo(String externalDealNo, String disCode) {
        return dealOrderPoMapper.getByExternalDealNo(externalDealNo, disCode);
    }

    public List<DealOrderPo> queryByDealNoList(List<String> dealNoList) {
        return dealOrderPoMapper.queryByDealNoList(dealNoList);
    }

    public int countUnPayOrPayingOrder(String txAcctNo, String disCode, List<String> cpAcctNos, String taTradeDt) {
        return dealOrderPoMapper.countUnPayOrPayingOrder(txAcctNo, disCode, cpAcctNos, taTradeDt);
    }

    public int isUnCheckDealOrder(String txAcctNo, String disCode, List<String> cpAcctNos) {
        return dealOrderPoMapper.isUnCheckDealOrder(txAcctNo, disCode, cpAcctNos);
    }

    public int updateByDealNoSelective(DealOrderPo dealOrderPo) {
        return dealOrderPoMapper.updateByDealNoSelective(dealOrderPo);
    }

    public void insertSelective(DealOrderPo orderPo) {
        dealOrderPoMapper.insertSelective(orderPo);
    }
}
