<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
        <relativePath />
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.howbuy.tms</groupId>
    <artifactId>high-order-center</artifactId>
    <version>4.9.24-RELEASE</version>
    <packaging>pom</packaging>
    <name>high-order-center</name>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <hessian.version>4.0.7</hessian.version>
        <dubbo-rpc-hessian.version>3.3.0</dubbo-rpc-hessian.version>
        
        <spring-boot.version>2.3.12.RELEASE</spring-boot.version>
        <spring-cloud.version>Hoxton.SR12</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.9.RELEASE</spring-cloud-alibaba.version>

        
        <druid.version>1.2.8</druid.version>
        <pagehelper.version>4.1.4</pagehelper.version>
        <mysql.connector.version>8.0.28</mysql.connector.version>
        <mybatis.spring.boot.starter.version>2.2.2</mybatis.spring.boot.starter.version>

        
        <dubbo.version>3.2.12</dubbo.version>
        <zookeeper.version>3.4.13</zookeeper.version>

        
        <log4j2.version>2.15.0</log4j2.version>
        <log4j-over-slf4j.version>1.7.29</log4j-over-slf4j.version>

        
        <fastjson.version>1.2.80</fastjson.version>
        <disruptor.version>3.4.2</disruptor.version>
        <javax.servlet.version>3.1.0</javax.servlet.version>
        <joda-time.version>2.10.10</joda-time.version>
        <commonshttpclient.version>3.0</commonshttpclient.version>
        <jxl.version>2.4.2</jxl.version>
        <poi.version>3.11</poi.version>

        
        <powermock.version>2.0.9</powermock.version>
        <jacoco.version>0.8.7</jacoco.version>

        
        <com.howbuy.howbuy-message-service.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-service.version>
        <com.howbuy.howbuy-message-amq.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-amq.version>
        <com.howbuy.howbuy-message-rocket.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-rocket.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
        <com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
        <com.howbuy.notifyUtil.version>1.0.0</com.howbuy.notifyUtil.version>
        <howbuy.mq.client.version>3.0.0-SNAPSHOT</howbuy.mq.client.version>
        <com.howbuy.message-public-client.version>5.1.16-RELEASE</com.howbuy.message-public-client.version>


        <com.howbuy.high-order-center-client.version>4.9.24-RELEASE</com.howbuy.high-order-center-client.version>
        <com.howbuy.high-order-center.version>4.9.24-RELEASE</com.howbuy.high-order-center.version>
        <com.howbuy.tms-common-service.version>4.8.90-RELEASE</com.howbuy.tms-common-service.version>
        <com.howbuy.tms-common-cache.version>4.8.90-RELEASE</com.howbuy.tms-common-cache.version>
        <com.howbuy.tms-common-client.version>4.8.90-RELEASE</com.howbuy.tms-common-client.version>
        <com.howbuy.tms-common-lang.version>4.8.90-RELEASE</com.howbuy.tms-common-lang.version>
        <com.howbuy.tms-common-outerservice.version>4.8.72-RELEASE</com.howbuy.tms-common-outerservice.version>
        <com.howbuy.tms-common-validator.version>4.8.72-RELEASE</com.howbuy.tms-common-validator.version>
        <com.howbuy.tms-common-enums.version>4.8.90-RELEASE</com.howbuy.tms-common-enums.version>
        <com.howbuy.tms-common-message-service.version>4.8.90-RELEASE</com.howbuy.tms-common-message-service.version>
        <com.howbuy.howbuy-cachemanagement.version>3.8.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.tms-common-dynamicds.version>1.0.1-RELEASE</com.howbuy.tms-common-dynamicds.version>
        <com.howbuy.howbuy-trace.version>1.0.5-RELEASE</com.howbuy.howbuy-trace.version>
    <com.howbuy.howbuy-boot-actuator-dubbo3.version>2.3.0-RELEASE</com.howbuy.howbuy-boot-actuator-dubbo3.version>
</properties>

    <dependencyManagement>
        <dependencies>
            
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-dependencies-zookeeper</artifactId>
                <version>${dubbo.version}</version>
                <type>pom</type>
            </dependency>

            
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.spring.boot.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-api</artifactId>
                <version>2.2.1</version>
            </dependency>

            
            <dependency>
                <groupId>org.apache.dubbo.extensions</groupId>
                <artifactId>dubbo-rpc-hessian</artifactId>
                <version>${dubbo-rpc-hessian.version}</version>
            </dependency>
            <dependency>
                <groupId>com.caucho</groupId>
                <artifactId>hessian</artifactId>
                <version>${hessian.version}</version>
            </dependency>
            
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j2.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.servlet.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-httpclient</groupId>
                <artifactId>commons-httpclient</artifactId>
                <version>${commonshttpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>jexcelapi</groupId>
                <artifactId>jxl</artifactId>
                <version>${jxl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>

            
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>2.23.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>org.jacoco.agent</artifactId>
                <version>${jacoco.version}</version>
                <classifier>runtime</classifier>
            </dependency>
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <scope>test</scope>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.pa.framework.mq</groupId>
                <artifactId>howbuy-mq-client</artifactId>
                <version>${howbuy.mq.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-service</artifactId>
                <version>${com.howbuy.howbuy-message-service.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-beanutils</artifactId>
                        <groupId>commons-beanutils</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-collections</artifactId>
                        <groupId>commons-collections</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-amq</artifactId>
                <version>${com.howbuy.howbuy-message-amq.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>activemq-all</artifactId>
                        <groupId>org.apache.activemq</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-message-rocket</artifactId>
                <version>${com.howbuy.howbuy-message-rocket.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-ccms-watcher</artifactId>
                <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>nacos-client</artifactId>
                        <groupId>com.alibaba.nacos</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-cachemanagement</artifactId>
                <version>${com.howbuy.howbuy-cachemanagement.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>activemq-all</artifactId>
                        <groupId>org.apache.activemq</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.howbuy</groupId>
                        <artifactId>HowbuyMessage</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-core</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context-support</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>4.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>notifyUtil</artifactId>
                <version>${com.howbuy.notifyUtil.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.common</groupId>
                <artifactId>common-service</artifactId>
                <version>${com.howbuy.common-service.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.howbuy.pa.cache</groupId>
                        <artifactId>howbuy-cache-client-trade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.howbuy.cc.message</groupId>
                <artifactId>message-public-client</artifactId>
                <version>${com.howbuy.message-public-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-service</artifactId>
                <version>${com.howbuy.howbuy-dfile-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-impl-local</artifactId>
                <version>${com.howbuy.howbuy-dfile-impl-local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.dfile</groupId>
                <artifactId>howbuy-dfile-impl-nfs</artifactId>
                <version>${com.howbuy.howbuy-dfile-impl-nfs.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator-dubbo3</artifactId>
                <version>${com.howbuy.howbuy-boot-actuator-dubbo3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.boot</groupId>
                <artifactId>howbuy-boot-actuator</artifactId>
                <version>2.1.0-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-core</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-log-pattern</artifactId>
                <version>1.0.0-RELEASE</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>high-order-center-client</artifactId>
                <version>${com.howbuy.high-order-center-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>high-order-center-service</artifactId>
                <version>${com.howbuy.high-order-center.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>high-order-center-dao</artifactId>
                <version>${com.howbuy.high-order-center.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-service</artifactId>
                <version>${com.howbuy.tms-common-service.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-cache</artifactId>
                <version>${com.howbuy.tms-common-cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-client</artifactId>
                <version>${com.howbuy.tms-common-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-lang</artifactId>
                <version>${com.howbuy.tms-common-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-outerservice</artifactId>
                <version>${com.howbuy.tms-common-outerservice.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-validator</artifactId>
                <version>${com.howbuy.tms-common-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-enums</artifactId>
                <version>${com.howbuy.tms-common-enums.version}</version>
            </dependency>
            <dependency>
                <groupId>com.howbuy.tms</groupId>
                <artifactId>tms-common-message-service</artifactId>
                <version>${com.howbuy.tms-common-message-service.version}</version>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-asyncqueue</artifactId>
                <version>1.0.0-release</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            
            <dependency>
                <groupId>com.howbuy</groupId>
                <artifactId>howbuy-trace</artifactId>
                <version>${com.howbuy.howbuy-trace.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>




































    <distributionManagement>
        <repository>
            <id>howbuy-release</id>
            <name>howbuy-release</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshot</id>
            <name>howbuy-snapshot</name>
            <url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <skipTests>true</skipTests>
                    <includes>
                        
                    </includes>
                </configuration>
            </plugin>

        </plugins>
    </build>
    </project>