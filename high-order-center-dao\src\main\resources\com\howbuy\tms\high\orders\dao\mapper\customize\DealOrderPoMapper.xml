<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.orders.dao.mapper.customize.DealOrderPoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.orders.dao.po.DealOrderPo"
               extends="com.howbuy.tms.high.orders.dao.mapper.DealOrderPoAutoMapper.BaseResultMap">
    </resultMap>

    <resultMap id="CancelOrderVoMap" type="com.howbuy.tms.high.orders.dao.vo.CancelOrderVo">
        <id column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode"/>
        <result column="DIS_TX_ACCT_NO" jdbcType="VARCHAR" property="disTxAcctNo"/>
        <result column="OUTLET_CODE" jdbcType="VARCHAR" property="outletCode"/>
        <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo"/>
        <result column="SUB_TX_ACCT_NO" jdbcType="VARCHAR" property="subTxAcctNo"/>
        <result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct"/>
        <result column="CUST_NAME" jdbcType="VARCHAR" property="custName"/>
        <result column="ID_TYPE" jdbcType="CHAR" property="idType"/>
        <result column="ID_NO" jdbcType="VARCHAR" property="idNo"/>
        <result column="TX_CODE" jdbcType="VARCHAR" property="txCode"/>
        <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName"/>
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="PROTOCOL_NAME" jdbcType="VARCHAR" property="protocolName"/>
        <result column="PROTOCOL_NO" jdbcType="VARCHAR" property="protocolNo"/>
        <result column="PAYMENT_TYPE" jdbcType="CHAR" property="paymentType"/>
        <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt"/>
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol"/>
        <result column="APP_RATIO" jdbcType="DECIMAL" property="appRatio"/>
        <result column="APP_DATE" jdbcType="VARCHAR" property="appDate"/>
        <result column="APP_TIME" jdbcType="VARCHAR" property="appTime"/>
        <result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm"/>
        <result column="UPDATE_DTM" jdbcType="TIMESTAMP" property="updateDtm"/>
        <result column="PAY_STATUS" jdbcType="CHAR" property="payStatus"/>
        <result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="TX_CHANNEL" jdbcType="CHAR" property="txChannel"/>
        <result column="INVST_TYPE" jdbcType="CHAR" property="invstType"/>
        <result column="IP_ADDRESS" jdbcType="VARCHAR" property="ipAddress"/>
        <result column="DATA_TRACK" jdbcType="VARCHAR" property="dataTrack"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt"/>
        <result column="EXTERNAL_DEAL_NO" jdbcType="VARCHAR" property="externalDealNo"/>
        <result column="PROTOCOL_TYPE" jdbcType="CHAR" property="protocolType"/>
        <result column="PRODUCT_CLASS" jdbcType="CHAR" property="productClass"/>
        <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel"/>
        <result column="ADVANCE_FLAG" jdbcType="VARCHAR" property="advanceFlag"/>
        <result column="PRODUCT_TYPE" jdbcType="VARCHAR" property="productType"/>
        <collection property="fundDealOrderDtlPos" column="DEAL_NO"
                    select="com.howbuy.tms.high.orders.dao.mapper.customize.HighDealOrderDtlPoMapper.selectCancelByDealNo"
                    javaType="ArrayList" ofType="com.howbuy.tms.high.orders.dao.vo.HighDealOrderDtlVo"/>
    </resultMap>

    <select id="selectFundCancelOrder" resultMap="CancelOrderVoMap"
            parameterType="com.howbuy.tms.high.orders.dao.po.DealOrderPo">
        select d.*
        from DEAL_ORDER d
        left join DEAL_ORDER_EXTEND e on d.deal_no = e.deal_no
        where d.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
          AND d.DEAL_NO = #{dealNo,jdbcType=VARCHAR}
          AND (e.rec_stat = '0' or e.rec_stat is null)
    </select>

    <select id="selectFundCancelOrderSubOrders" resultMap="CancelOrderVoMap"
            parameterType="com.howbuy.tms.high.orders.dao.po.DealOrderPo">
        select d.*
        from DEAL_ORDER d
                 inner join high_deal_order_dtl l
                            on d.deal_no = l.deal_no
                 left join DEAL_ORDER_EXTEND e
                           on d.deal_no = e.deal_no
        where d.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
          and l.MAIN_DEAL_ORDER_NO = #{mainDealNo,jdbcType=VARCHAR}
          AND (e.rec_stat = '0' or e.rec_stat is null)
    </select>

    <update id="updateCancelOrder" parameterType="com.howbuy.tms.high.orders.dao.po.DealOrderPo">
        update DEAL_ORDER
        set ORDER_STATUS = #{orderStatus,jdbcType=VARCHAR},
        <if test="withdrawDirection != null">
            WITHDRAW_DIRECTION = #{withdrawDirection,jdbcType=VARCHAR},
        </if>
        UPDATE_DTM = #{updateDtm,jdbcType=TIMESTAMP},
        ADVANCE_FLAG =#{advanceFlag,jdbcType=VARCHAR}
        where DEAL_NO = #{dealNo,jdbcType=VARCHAR}
        AND PAY_STATUS = #{payStatus,jdbcType=CHAR}
    </update>

    <resultMap id="QueryOrderListMap" type="com.howbuy.tms.high.orders.dao.vo.DealOrderVo">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode"/>
        <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct"/>
        <result column="PAYMENT_TYPE" jdbcType="VARCHAR" property="paymentType"/>
        <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName"/>
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="SUB_PRODUCT_CODE" jdbcType="VARCHAR" property="subProductCode"/>
        <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt"/>
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol"/>
        <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt"/>
        <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol"/>
        <result column="ACK_DT" jdbcType="DECIMAL" property="ackDt"/>
        <result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm"/>
        <result column="PAY_STATUS" jdbcType="CHAR" property="payStatus"/>
        <result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="CONTINUANCE_FLAG" jdbcType="VARCHAR" property="continuanceFlag"/>
        <result column="STAGE_FLAG" jdbcType="VARCHAR" property="stageFlag"/>
        <result column="TX_ACK_FLAG" jdbcType="CHAR" property="txAckFlag"/>
        <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt"/>
        <result column="FEE" jdbcType="DECIMAL" property="fee"/>
        <result column="M_BUSI_CODE" jdbcType="VARCHAR" property="mBusiCode"/>
        <result column="REDEEM_DIRECTION" jdbcType="VARCHAR" property="redeemDirection"/>
        <result column="FIRST_BUY_FLAG" jdbcType="VARCHAR" property="firstBuyFlag"/>
        <result column="SCALE_TYPE" jdbcType="VARCHAR" property="scaleType"/>
        <result column="FUND_SHARE_CLASS" jdbcType="VARCHAR" property="fundShareClass"/>
        <result column="SUPPORT_ADVANCE_FLAG" jdbcType="VARCHAR" property="supportAdvanceFlag"/>
        <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel"/>
        <result column="MEMO" jdbcType="VARCHAR" property="memo"/>
        <result column="NAV" jdbcType="DECIMAL" property="nav"/>
        <result column="ADVANCE_FLAG" jdbcType="VARCHAR" property="advanceFlag"/>
        <result column="PRODUCT_TYPE" jdbcType="VARCHAR" property="productType"/>
        <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt"/>
        <result column="Product_Sub_Type" jdbcType="VARCHAR" property="productSubType"/>
        <result column="APPOINTMENT_DEAL_NO" jdbcType="VARCHAR" property="appointmentDealNo"/>
        <result column="IS_VOL_TANSFER" jdbcType="VARCHAR" property="isVolTansfer"/>
        <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag"/>
        <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo"/>
        <result column="CURRENCY" jdbcType="VARCHAR" property="currency"/>
        <result column="HIGH_FUND_INV_PLAN_FLAG" jdbcType="VARCHAR" property="highFundInvPlanFlag"/>
        <result column="TX_CHANNEL" jdbcType="VARCHAR" property="txChannel"/>
        <result column="DEAL_TYPE" jdbcType="VARCHAR" property="dealType"/>
        <result column="JOIN_DT" jdbcType="VARCHAR" property="joinDt"/>
        <result column="TRANSFER_PRICE" jdbcType="DECIMAL" property="transferPrice"/>
        <result column="IS_NO_TRADE_TRANSFER" jdbcType="VARCHAR" property="isNoTradeTransfer"/>
        <result column="transfer_in_fund_code" jdbcType="VARCHAR" property="transferInFundCode"/>
        <result column="transfer_in_fund_name" jdbcType="VARCHAR" property="transferInFundName"/>
        <result column="transfer_in_ack_vol" jdbcType="DECIMAL" property="transferInAckVol"/>
        <result column="transfer_in_ack_amt" jdbcType="DECIMAL" property="transferInAckAmt"/>
        <result column="transfer_in_main_fund_code" jdbcType="VARCHAR" property="transferInMainFundCode"/>
        <result column="transfer_in_ack_nav" jdbcType="DECIMAL" property="transferInAckNav"/>
        <result column="PAY_OUT_DT" jdbcType="VARCHAR" property="payOutDt"/>
        <result column="subsAmt" jdbcType="DECIMAL" property="subsAmt"/>
    </resultMap>

    <!-- 查询递延订单 -->
    <select id="queryAdvanceDealOrderList" parameterType="map" resultMap="QueryOrderListMap">
        select
        t1.DEAL_NO,
        t1.TX_ACCT_NO,
        t1.DIS_CODE,
        t1.CP_ACCT_NO,
        t1.BANK_CODE,
        t1.BANK_ACCT,
        t1.PRODUCT_CODE,
        t1.PRODUCT_NAME,
        t1.PAYMENT_TYPE,
        t1.APP_AMT,
        t1.APP_VOL,
        t2.ACK_AMT,
        t2.ACK_VOL,
        t1.APP_DTM,
        t1.PAY_STATUS,
        t1.ORDER_STATUS,
        t1.TA_TRADE_DT,
        t2.FEE,
        t2.M_BUSI_CODE,
        t2.REDEEM_DIRECTION,
        '2' as scale_type,
        t2.MEMO,
        t2.NAV,
        t1.ADVANCE_FLAG
        from DEAL_ORDER t1
        inner join HIGH_DEAL_ORDER_DTL t2 on t1.deal_no=t2.deal_no
        left join DEAL_ORDER_EXTEND t3 on t1.deal_no=t3.deal_no
        <where>
            t1.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
            <if test="condition.disCodeList != null">
                and t1.DIS_CODE in
                <foreach collection="condition.disCodeList" index="index" item="disCode" open="(" separator=","
                         close=")">
                    #{disCode}
                </foreach>
            </if>
            and t1.deal_type = '2'
            and (t3.rec_stat = '0' or t3.rec_stat is null)
            and t1.ADVANCE_FLAG = '2'
            and t1.ORDER_STATUS = '6'
            and t1.PAY_STATUS = '5'
            <if test="condition.cpAcctNo != null and condition.cpAcctNo != '' ">
                and t1.CP_ACCT_NO = #{condition.cpAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.mBusiCodeList != null">
                and t2.M_BUSI_CODE in
                <foreach item="item" collection="condition.mBusiCodeList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="condition.appBeginDtm != null">
                and t1.APP_DTM  <![CDATA[>=]]> #{condition.appBeginDtm,jdbcType=TIMESTAMP}
            </if>
            <if test="condition.appEndDtm != null">
                and t1.APP_DTM <![CDATA[<]]> #{condition.appEndDtm,jdbcType=TIMESTAMP}
            </if>
            <if test="condition.fundNameOrCode != null and condition.fundNameOrCode != ''">
                and (t1.PRODUCT_NAME like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%') or
                t1.PRODUCT_CODE like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%') )
            </if>
        </where>
    </select>

    <!-- 查询订单列表 (scale_type: 1-直销; 2-代销) -->
    <select id="queryDealOrderListWithDirect" parameterType="map" resultMap="QueryOrderListMap">
        select t.* from (
        select
        t1.DEAL_NO,
        t1.TX_ACCT_NO,
        t1.DIS_CODE,
        t1.CP_ACCT_NO,
        t1.BANK_CODE,
        t1.BANK_ACCT,
        t1.PRODUCT_CODE,
        '' as SUB_PRODUCT_CODE,
        t1.PRODUCT_NAME,
        t1.PAYMENT_TYPE,
        t1.APP_AMT,
        t1.APP_VOL,
        t2.ACK_AMT,
        t2.ACK_VOL,
        t1.APP_DTM,
        t1.PAY_STATUS,
        t1.ORDER_STATUS,
        t1.TA_TRADE_DT,
        t2.FEE,
        t2.M_BUSI_CODE,
        t2.REDEEM_DIRECTION,
        '2' as scale_type,
        t2.MEMO,
        t2.NAV,
        t1.ADVANCE_FLAG,
        t1.PRODUCT_TYPE,
        t2.submit_ta_dt,
        t1.Product_Sub_Type,
        t2.ACK_DT,
        '' IS_VOL_TANSFER,
        t2.MERGE_SUBMIT_FLAG,
        t2.MAIN_DEAL_ORDER_NO,
        '156' as CURRENCY,
        t2.HIGH_FUND_INV_PLAN_FLAG,
        t2.JOIN_DT,
        t2.CONTINUANCE_FLAG,
        t2.STAGE_FLAG,
        t2.TX_ACK_FLAG,
        t2.TRANSFER_PRICE,
        t2.IS_NO_TRADE_TRANSFER,
        null as transfer_in_fund_code,
        null as transfer_in_fund_name,
        null as transfer_in_ack_vol,
        null as transfer_in_ack_amt,
        null as transfer_in_main_fund_code,
        null as transfer_in_ack_nav,
        t2.subs_amt as subsAmt,
        t2.PAY_OUT_DT
        from DEAL_ORDER t1
        inner join HIGH_DEAL_ORDER_DTL t2 on t1.deal_no=t2.deal_no
        left join DEAL_ORDER_EXTEND t3 on t1.deal_no=t3.deal_no
        <where>
            t1.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
            <if test="condition.dealNo != null and condition.dealNo != '' ">
                and t1.deal_no = #{condition.dealNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.disCodeList != null">
                and t1.DIS_CODE in
                <foreach collection="condition.disCodeList" index="index" item="disCode" open="(" separator=","
                         close=")">
                    #{disCode}
                </foreach>
            </if>
            and t1.deal_type = '2'
            and (t3.rec_stat = '0' or t3.rec_stat is null)
            AND (t2.MERGE_SUBMIT_FLAG is null or t2.MERGE_SUBMIT_FLAG !='1' or t2.DEAL_NO = t2.MAIN_DEAL_ORDER_NO)
            <if test="condition.cpAcctNo != null and condition.cpAcctNo != '' ">
                and t1.CP_ACCT_NO = #{condition.cpAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.mBusiCodeList != null">
                and t2.M_BUSI_CODE in
                <foreach item="item" collection="condition.mBusiCodeList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.orderStatusList != null">
                and t1.ORDER_STATUS in
                <foreach item="item" collection="condition.orderStatusList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.productCode != null and condition.productCode != ''">
                and t1.PRODUCT_CODE = #{condition.productCode,jdbcType=VARCHAR}
            </if>
            <if test="condition.notFilterHzFund!= null and condition.notFilterHzFund == 0">
                AND t1.DIS_CODE !='HZ000N001'
            </if>

            <if test="condition.productName != null and condition.productName != ''">
                and t1.PRODUCT_NAME = #{condition.productName,jdbcType=VARCHAR}
            </if>
            <if test="condition.appBeginDtm != null">
                and t1.APP_DTM  <![CDATA[>=]]> #{condition.appBeginDtm,jdbcType=TIMESTAMP}
            </if>
            <if test="condition.appEndDtm != null">
                and t1.APP_DTM <![CDATA[<]]> #{condition.appEndDtm,jdbcType=TIMESTAMP}
            </if>
            <if test="condition.fundNameOrCode != null and condition.fundNameOrCode != ''">
                and (t1.PRODUCT_NAME like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%') or
                t1.PRODUCT_CODE like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%') )
            </if>
        </where>
        union all
        select
        cm.appserialno as deal_no,
        cm.hboneno as tx_acct_no,
        cm.discode as dis_code,
        null as cp_acct_no,
        null as bank_code,
        cm.bankacct as bank_acct,
        ifnull(cm.mjjdm,cm.fundcode) as product_code,
        cm.fundcode as SUB_PRODUCT_CODE,
        cm.fundname as product_name,
        null as payment_type,
        cm.appamt as app_amt,
        cm.appvol as app_vol,
        cm.ackamt as ack_amt,
        cm.ackvol as ack_vol,
        STR_TO_DATE(cm.tradedt, '%Y%m%d') as app_dtm,
        cm.paystate AS pay_status,
        cm.orderstate,
        cm.tradedt as ta_trade_dt,
        cm.fee as fee,
        CONCAT('1', cm.busicode) AS m_busi_code,
        null as redeem_direction,
        '1' as scale_type,
        null as memo,
        cm.nav as nav,
        '' as advance_flag,
        '' as product_type,
        ifnull(cm.NEW_TRADE_DT, cm.tradedt) as submit_ta_dt,
        '' as Product_Sub_Type,
        case
        when  cm.orderstate = '1' then null
        when  cm.orderstate != '1' then cm.tradedt
        end as ack_dt,
        cm.IS_VOL_TANSFER,
        null as MERGE_SUBMIT_FLAG,
        null as MAIN_DEAL_ORDER_NO,
        cm.CURRENCY,
        null as HIGH_FUND_INV_PLAN_FLAG,
        null as JOIN_DT,
        null as CONTINUANCE_FLAG,
        case
        when  cm.mjjdm is not null then '1'
        when  cm.mjjdm is null then  ''
        end as STAGE_FLAG,
        null as TX_ACK_FLAG,
        cm.TRANSFER_PRICE ,
        cm.IS_NO_TRADE_TRANSFER,
        cm.transfer_in_fund_code,
        cm.transfer_in_fund_name,
        cm.transfer_in_ack_vol,
        cm.transfer_in_ack_amt,
        cm.transfer_in_main_fund_code,
        cm.transfer_in_ack_nav,
        null as subsAmt,
        null as PAY_OUT_DT
        from cm_custtrade_direct cm
        <where>
            cm.hboneno = #{condition.hbOneNo,jdbcType=VARCHAR}
            <if test="condition.dealNo != null and condition.dealNo != '' ">
                and cm.appserialno = #{condition.dealNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.disCodeList != null">
                and cm.discode in
                <foreach collection="condition.disCodeList" index="index" item="disCode" open="(" separator=","
                         close=")">
                    #{disCode}
                </foreach>
            </if>
            <if test="condition.notFilterHzFund != null and condition.notFilterHzFund == 0">
                AND cm.discode !='HZ000N001'
            </if>
            <if test="condition.notFilterHkFund != null and condition.notFilterHkFund == 0">
                and cm.IS_HK_PRODUCT!='1'
            </if>
            and cm.recstat = '0'
            <if test="condition.orderStatusList != null">
                and cm.orderstate in
                <foreach item="item" collection="condition.orderStatusList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.busiCodeList != null">
                and cm.busicode in
                <foreach item="item" collection="condition.busiCodeList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.productCode != null and condition.productCode != ''">
                and (cm.fundcode = #{condition.productCode,jdbcType=VARCHAR}
                or cm.mjjdm = #{condition.productCode,jdbcType=VARCHAR}
                or cm.transfer_in_fund_code = #{condition.productCode,jdbcType=VARCHAR}
                or cm.transfer_in_main_fund_code = #{condition.productCode,jdbcType=VARCHAR})
            </if>
            <if test="condition.productName != null and condition.productName != ''">
                and cm.fundname = #{condition.productName,jdbcType=VARCHAR}
                or cm.transfer_in_fund_name = #{condition.productName,jdbcType=VARCHAR}
            </if>
            <if test="condition.appBeginDtm != null">
                and cm.tradedt <![CDATA[>=]]> DATE_FORMAT(#{condition.appBeginDtm,jdbcType=TIMESTAMP}, '%Y%m%d')
            </if>
            <if test="condition.appEndDtm != null">
                and cm.tradedt <![CDATA[<]]> DATE_FORMAT(#{condition.appEndDtm,jdbcType=TIMESTAMP}, '%Y%m%d')
            </if>
            <if test="condition.fundNameOrCode != null and condition.fundNameOrCode != ''">
                and (cm.fundname like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                or ifnull(cm.mjjdm,cm.fundcode) like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                or cm.transfer_in_fund_name like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                or ifnull(cm.transfer_in_main_fund_code,cm.transfer_in_fund_code) like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                )
            </if>
        </where>
        ) t
        order by t.app_dtm desc,deal_no desc
    </select>

    <select id="countHkDealNum" parameterType="map" resultType="java.lang.Integer">
        select count(*) from (
        select
        cm.appserialno as deal_no
        from cm_custtrade_direct cm
        <where>
            cm.hboneno = #{condition.hbOneNo,jdbcType=VARCHAR}
            and cm.IS_HK_PRODUCT='1'
            <if test="condition.dealNo != null and condition.dealNo != '' ">
                and cm.appserialno = #{condition.dealNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.disCodeList != null">
                and cm.discode in
                <foreach collection="condition.disCodeList" index="index" item="disCode" open="(" separator=","
                         close=")">
                    #{disCode}
                </foreach>
            </if>
            and cm.recstat = '0'
            <if test="condition.orderStatusList != null">
                and cm.orderstate in
                <foreach item="item" collection="condition.orderStatusList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.busiCodeList != null">
                and cm.busicode in
                <foreach item="item" collection="condition.busiCodeList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.productCode != null and condition.productCode != ''">
                and (cm.fundcode = #{condition.productCode,jdbcType=VARCHAR}
                or cm.mjjdm = #{condition.productCode,jdbcType=VARCHAR}
                or cm.transfer_in_fund_code = #{condition.productCode,jdbcType=VARCHAR}
                or cm.transfer_in_main_fund_code = #{condition.productCode,jdbcType=VARCHAR})
            </if>
            <if test="condition.productName != null and condition.productName != ''">
                and cm.fundname = #{condition.productName,jdbcType=VARCHAR}
                or cm.transfer_in_fund_name = #{condition.productName,jdbcType=VARCHAR}
            </if>
            <if test="condition.appBeginDtm != null">
                and cm.tradedt <![CDATA[>=]]> DATE_FORMAT(#{condition.appBeginDtm,jdbcType=TIMESTAMP}, '%Y%m%d')
            </if>
            <if test="condition.appEndDtm != null">
                and cm.tradedt <![CDATA[<]]> DATE_FORMAT(#{condition.appEndDtm,jdbcType=TIMESTAMP}, '%Y%m%d')
            </if>
            <if test="condition.fundNameOrCode != null and condition.fundNameOrCode != ''">
                and (cm.fundname like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                or ifnull(cm.mjjdm,cm.fundcode) like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                or cm.transfer_in_fund_name like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                or ifnull(cm.transfer_in_main_fund_code,cm.transfer_in_fund_code) like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                )
            </if>
        </where>
        ) t
    </select>

    <select id="countHzDealNum" parameterType="map" resultType="java.lang.Integer">
        select count(*) from (
        select
        t1.DEAL_NO as deal_no
        from DEAL_ORDER t1
        inner join HIGH_DEAL_ORDER_DTL t2 on t1.deal_no=t2.deal_no
        left join DEAL_ORDER_EXTEND t3 on t1.deal_no=t3.deal_no
        <where>
            t1.DIS_CODE ='HZ000N001'
            and t1.deal_type = '2'
            and (t3.rec_stat = '0' or t3.rec_stat is null)
            AND (t2.MERGE_SUBMIT_FLAG is null or t2.MERGE_SUBMIT_FLAG !='1' or t2.DEAL_NO = t2.MAIN_DEAL_ORDER_NO)
            <if test="condition.dealNo != null and condition.dealNo != '' ">
                and t1.deal_no = #{condition.dealNo,jdbcType=VARCHAR}
            </if>
            and t1.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
            <if test="condition.disCodeList != null">
                and t1.DIS_CODE in
                <foreach collection="condition.disCodeList" index="index" item="disCode" open="(" separator=","
                         close=")">
                    #{disCode}
                </foreach>
            </if>
            AND (t2.MERGE_SUBMIT_FLAG is null or t2.MERGE_SUBMIT_FLAG !='1' or t2.DEAL_NO = t2.MAIN_DEAL_ORDER_NO)
            <if test="condition.cpAcctNo != null and condition.cpAcctNo != '' ">
                and t1.CP_ACCT_NO = #{condition.cpAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="condition.mBusiCodeList != null">
                and t2.M_BUSI_CODE in
                <foreach item="item" collection="condition.mBusiCodeList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.orderStatusList != null">
                and t1.ORDER_STATUS in
                <foreach item="item" collection="condition.orderStatusList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.productCode != null and condition.productCode != ''">
                and t1.PRODUCT_CODE = #{condition.productCode,jdbcType=VARCHAR}
            </if>
            <if test="condition.productName != null and condition.productName != ''">
                and t1.PRODUCT_NAME = #{condition.productName,jdbcType=VARCHAR}
            </if>
            <if test="condition.appBeginDtm != null">
                and t1.APP_DTM  <![CDATA[>=]]> #{condition.appBeginDtm,jdbcType=TIMESTAMP}
            </if>
            <if test="condition.appEndDtm != null">
                and t1.APP_DTM <![CDATA[<]]> #{condition.appEndDtm,jdbcType=TIMESTAMP}
            </if>
            <if test="condition.fundNameOrCode != null and condition.fundNameOrCode != ''">
                and (t1.PRODUCT_NAME like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%') or
                t1.PRODUCT_CODE like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%') )
            </if>
        </where>
        union all
        select
        cm.appserialno as deal_no
        from cm_custtrade_direct cm
        <where>
            cm.hboneno = #{condition.hbOneNo,jdbcType=VARCHAR}
            <if test="condition.dealNo != null and condition.dealNo != '' ">
                and cm.appserialno = #{condition.dealNo,jdbcType=VARCHAR}
            </if>
            and cm.discode ='HZ000N001'
            and cm.recstat = '0'
            <if test="condition.orderStatusList != null">
                and cm.orderstate in
                <foreach item="item" collection="condition.orderStatusList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.busiCodeList != null">
                and cm.busicode in
                <foreach item="item" collection="condition.busiCodeList" separator="," open="(" close=")" index="">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="condition.productCode != null and condition.productCode != ''">
                and (cm.fundcode = #{condition.productCode,jdbcType=VARCHAR}
                or cm.mjjdm = #{condition.productCode,jdbcType=VARCHAR}
                or cm.transfer_in_fund_code = #{condition.productCode,jdbcType=VARCHAR}
                or cm.transfer_in_main_fund_code = #{condition.productCode,jdbcType=VARCHAR})
            </if>
            <if test="condition.productName != null and condition.productName != ''">
                and cm.fundname = #{condition.productName,jdbcType=VARCHAR}
                or cm.transfer_in_fund_name = #{condition.productName,jdbcType=VARCHAR}
            </if>
            <if test="condition.appBeginDtm != null">
                and cm.tradedt <![CDATA[>=]]> DATE_FORMAT(#{condition.appBeginDtm,jdbcType=TIMESTAMP}, '%Y%m%d')
            </if>
            <if test="condition.appEndDtm != null">
                and cm.tradedt <![CDATA[<]]> DATE_FORMAT(#{condition.appEndDtm,jdbcType=TIMESTAMP}, '%Y%m%d')
            </if>
            <if test="condition.fundNameOrCode != null and condition.fundNameOrCode != ''">
                and (cm.fundname like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                or ifnull(cm.mjjdm,cm.fundcode) like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                or cm.transfer_in_fund_name like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%')
                or ifnull(cm.transfer_in_main_fund_code,cm.transfer_in_fund_code) like concat(concat('%',#{condition.fundNameOrCode,jdbcType=VARCHAR}),'%'))
            </if>
        </where>
        ) t
    </select>

    <!-- 查询未完成的认申购记录数 -->
    <select id="selectNotCompleteBuyCount" resultType="int" parameterType="map">
        select count(1)
        from deal_order t1
        inner join HIGH_DEAL_ORDER_DTL t3 on t1.deal_no = t3.deal_no
        left join DEAL_ORDER_EXTEND t2 on t1.deal_no = t2.deal_no
        where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
          and t1.product_code = #{fundCode,jdbcType=VARCHAR}
          and t1.deal_type = '2'
          and t1.order_status = '1'
          and t1.pay_status = '4'
          and t3.m_busi_code in ('1120', '1122')
          AND (t2.rec_stat = '0' or t2.rec_stat is null)
    </select>

    <!-- 查询客户首单数量 -->
    <select id="selectFirstBuyOrderCount" resultType="int" parameterType="map">
        select count(1)
        from deal_order t1
        inner join HIGH_DEAL_ORDER_DTL t3 on t1.deal_no = t3.deal_no
        left join DEAL_ORDER_EXTEND t2 on t1.deal_no = t2.deal_no
        where t1.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
          and t1.product_code = #{fundCode,jdbcType=VARCHAR}
          and t1.deal_type = '2'
          and t3.first_buy_flag = '1'
          and t1.order_status = '1'
          and t3.m_busi_code in ('1120', '1122')
          AND (t2.rec_stat = '0' or t2.rec_stat is null)
    </select>

    <!-- 查询高端可撤订单列表 -->
    <select id="selectCancelOrderList" parameterType="map" resultMap="QueryOrderListMap">
        select * from (
        select
        t1.DEAL_NO ,
        t1.DIS_CODE ,
        t1.TX_ACCT_NO ,
        t1.CP_ACCT_NO ,
        t1.BANK_ACCT ,
        t1.BANK_CODE ,
        t1.PRODUCT_NAME ,
        t1.PRODUCT_CODE ,
        t1.PAYMENT_TYPE ,
        t1.APP_AMT ,
        t1.APP_VOL ,
        t1.APP_RATIO ,
        t1.APP_DTM ,
        t1.PAY_STATUS ,
        t1.ORDER_STATUS ,
        t1.TA_TRADE_DT ,
        t1.TX_CHANNEL ,
        t2.fee ,
        t2.M_BUSI_CODE ,
        t2.REDEEM_DIRECTION ,
        t2.FIRST_BUY_FLAG,
        t2.FUND_SHARE_CLASS,
        t2.SUPPORT_ADVANCE_FLAG,
        t1.PRODUCT_CHANNEL,
        t2.SUBMIT_TA_DT,
        t2.MERGE_SUBMIT_FLAG,
        t2.MAIN_DEAL_ORDER_NO
        from DEAL_ORDER t1
        inner join HIGH_DEAL_ORDER_DTL t2 on t1.DEAL_NO = t2.DEAL_NO
        left join DEAL_ORDER_EXTEND t3 on t1.DEAL_NO = t3.deal_no
        <where>t1.deal_type = '2'
            and t1.order_status = '1'
            and t2.m_busi_code in ('1120','1122')
            AND (t2.MERGE_SUBMIT_FLAG is null or t2.MERGE_SUBMIT_FLAG !='1' or t2.DEAL_NO = t2.MAIN_DEAL_ORDER_NO)
            AND (t3.rec_stat = '0' or t3.rec_stat is null)
            <if test="txAcctNo != null and txAcctNo != '' ">
                and t1.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="currDate != null">
                and ( CALM_DTM is null or CALM_DTM  <![CDATA[>]]> #{currDate,jdbcType=TIMESTAMP})
                and STR_TO_DATE(CONCAT(T2.SUBMIT_TA_DT, '150000'), '%Y%m%d%H%i%s') <![CDATA[>]]> #{currDate,jdbcType=TIMESTAMP}
            </if>
        </where>
        union all
        select
        t1.DEAL_NO ,
        t1.DIS_CODE ,
        t1.TX_ACCT_NO ,
        t1.CP_ACCT_NO ,
        t1.BANK_ACCT ,
        t1.BANK_CODE ,
        t1.PRODUCT_NAME ,
        t1.PRODUCT_CODE ,
        t1.PAYMENT_TYPE ,
        t1.APP_AMT ,
        t1.APP_VOL ,
        t1.APP_RATIO ,
        t1.APP_DTM ,
        t1.PAY_STATUS ,
        t1.ORDER_STATUS ,
        t1.TA_TRADE_DT ,
        t1.TX_CHANNEL ,
        t2.fee ,
        t2.M_BUSI_CODE ,
        t2.REDEEM_DIRECTION ,
        t2.FIRST_BUY_FLAG,
        t2.FUND_SHARE_CLASS,
        t2.SUPPORT_ADVANCE_FLAG,
        t1.PRODUCT_CHANNEL,
        t2.SUBMIT_TA_DT,
        t2.MERGE_SUBMIT_FLAG,
        t2.MAIN_DEAL_ORDER_NO
        from DEAL_ORDER t1
        inner join HIGH_DEAL_ORDER_DTL t2 on t1.DEAL_NO = t2.DEAL_NO
        left join DEAL_ORDER_EXTEND t3 on t1.DEAL_NO = t3.DEAL_NO
        <where>
            t1.deal_type = '2'
            and t1.order_status = '1'
            and t2.m_busi_code in ('1124')
            AND (t2.MERGE_SUBMIT_FLAG is null or t2.MERGE_SUBMIT_FLAG !='1' or t2.DEAL_NO = t2.MAIN_DEAL_ORDER_NO)
            AND (t3.rec_stat = '0' or t3.rec_stat is null)
            <if test="txAcctNo != null and txAcctNo != '' ">
                and t1.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="currDate != null">
                and STR_TO_DATE(CONCAT(T2.SUBMIT_TA_DT, '150000'), '%Y%m%d%H%i%s') <![CDATA[>]]> #{currDate,jdbcType=TIMESTAMP}
            </if>
        </where>
        ) t
        order by t.DEAL_NO, t.APP_DTM
    </select>

    <!-- 查询高端可强撤订单列表 -->
    <select id="selectForceCancelOrderList" parameterType="map" resultMap="QueryOrderListMap">
        select
        t1.DEAL_NO ,
        t1.DIS_CODE ,
        t1.TX_ACCT_NO ,
        t1.CP_ACCT_NO ,
        t1.BANK_ACCT ,
        t1.BANK_CODE ,
        t1.PRODUCT_NAME ,
        t1.PRODUCT_CODE ,
        t1.PAYMENT_TYPE ,
        t1.APP_AMT ,
        t1.APP_VOL ,
        t1.APP_RATIO ,
        t1.APP_DTM ,
        t1.PAY_STATUS ,
        t1.ORDER_STATUS ,
        t1.TA_TRADE_DT ,
        t2.fee ,
        t2.M_BUSI_CODE ,
        t2.REDEEM_DIRECTION ,
        t2.SUBMIT_TA_DT,
        t3.APPOINTMENT_DEAL_NO,
        t2.MERGE_SUBMIT_FLAG,
        t2.MAIN_DEAL_ORDER_NO,
        t1.PRODUCT_CHANNEL
        from DEAL_ORDER t1
        inner join HIGH_DEAL_ORDER_DTL t2 on t1.DEAL_NO = t2.DEAL_NO
        left join DEAL_ORDER_EXTEND t3 on t1.DEAL_NO = t3.deal_no
        <where>
            t1.deal_type = '2'
            and t1.order_status = '1'
            and t2.m_busi_code in ('1120','1122','1124')
            AND (t2.MERGE_SUBMIT_FLAG is null or t2.MERGE_SUBMIT_FLAG !='1' or t2.DEAL_NO = t2.MAIN_DEAL_ORDER_NO)
            AND (t3.rec_stat = '0' or t3.rec_stat is null)
            <if test="txAcctNo != null and txAcctNo != '' ">
                and t1.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
            </if>
            <if test="taTradeDt != null">
                and (t2.SUBMIT_TA_DT <![CDATA[>=]]> #{taTradeDt,jdbcType=VARCHAR})
            </if>
            <if test="disCode != null and disCode != '' ">
                and t1.DIS_CODE = #{disCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by t1.DEAL_NO, t1.APP_DTM
    </select>

    <!-- 查询合并上报单汇总信息 -->
    <select id="selectMergeOrderTotalInfoList" parameterType="map" resultMap="QueryOrderListMap">
        select
        t2.MAIN_DEAL_ORDER_NO as DEAL_NO,
        sum(t1.APP_AMT) as APP_AMT,
        sum(t1.APP_VOL) as APP_VOL,
        sum(t2.ACK_AMT) as ACK_AMT,
        sum(t2.ACK_VOL) as ACK_VOL,
        GROUP_CONCAT(t1.PAY_STATUS ORDER BY t1.DEAL_NO SEPARATOR ',') AS PAY_STATUS,
        GROUP_CONCAT(t1.ORDER_STATUS ORDER BY t1.DEAL_NO SEPARATOR ',') AS ORDER_STATUS,
        sum(t2.fee) as fee,
        t2.MAIN_DEAL_ORDER_NO
        from DEAL_ORDER t1, HIGH_DEAL_ORDER_DTL t2
        where t1.DEAL_NO = t2.DEAL_NO
        and t2.MAIN_DEAL_ORDER_NO in
        <foreach collection="mainDealNoList" item="mainDealNo" open="(" close=")" separator=",">
            #{mainDealNo}
        </foreach>
        group by t2.MAIN_DEAL_ORDER_NO
    </select>

    <!-- 查询历史购买资金账号 -->
    <select id="selectHisCpAcctNo" parameterType="map" resultType="string">
        select distinct d.cp_acct_no
        from deal_order d inner join high_deal_order_dtl h on d.deal_no = h.deal_no
        where d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        and d.dis_code = #{disCode,jdbcType=VARCHAR}
        <if test="fundCode != null and fundCode != '' ">
            and d.product_code = #{fundCode,jdbcType=VARCHAR}
        </if>
        <if test="taCode != null and taCode != '' ">
            and h.ta_code = #{taCode,jdbcType=VARCHAR}
        </if>
        and d.protocol_type = '4'
        and d.order_status in('1','2','3')
    </select>
    <!-- 查询在途修改分红方式订单 -->
    <select id="selectrOnWayModifyDivOrderList" parameterType="map" resultMap="BaseResultMap">
        select d.*
        from DEAL_ORDER d
             inner join HIGH_DEAL_ORDER_DTL h on d.deal_no = h.deal_no
             left join DEAL_ORDER_EXTEND e on d.deal_no = e.deal_no
        where d.TX_ACCT_NO = #{txAcctNo,jdbcType=VARCHAR}
          AND d.deal_type = '2'
          AND d.order_status = '1'
          and h.m_busi_code = '1129'
          AND (e.rec_stat = '0' or e.rec_stat is null)
    </select>

    <!-- 查询指定日期确认的修改分红方式订单 -->
    <select id="selectAckModifyDivOrderListByAckDt" parameterType="map"
            resultType="com.howbuy.tms.high.orders.dao.vo.QueryModifyDivOrdelVo">
        select d.PRODUCT_CODE  fundCode,
               h.FUND_DIV_MODE fundDivMode,
               h.ACK_DT        ackDt
        from DEAL_ORDER d
                 inner join HIGH_DEAL_ORDER_DTL h
                            on d.deal_no = h.deal_no
                 left join DEAL_ORDER_EXTEND e
                           on d.deal_no = e.deal_no
        where d.TX_ACCT_NO = #{txAcctNo}
          AND d.deal_type = '2'
          AND d.order_status = '3'
          and h.ack_dt = #{ackDt}
          and h.m_busi_code = '1129'
          AND (e.rec_stat = '0' or e.rec_stat is null)
    </select>

    <!-- 查询在途交易笔数 -->
    <select id="selectOnWayTradeNum" parameterType="map" resultType="int">
        select sum(n)
        from (select count(1) n
        from DEAL_ORDER d,high_deal_order_dtl h,DEAL_ORDER_EXTEND e
        where d.deal_no = h.deal_no
        AND d.deal_no = e.deal_no
        AND d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        AND d.order_status = '1'
        AND d.deal_type = '2'
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="notFilterHzFund != null and notFilterHzFund == 0 ">
            and d.dis_code !='HZ000N001'
        </if>
        AND (e.rec_stat = '0' or e.rec_stat is null)
        union all
        select count(1) n
        from CM_CUSTTRADE_DIRECT t3
        where t3.txacctno = #{txAcctNo,jdbcType=VARCHAR} and t3.RECSTAT='0' and t3.paystate !='3' and t3.prebookstate = '2'
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t3.discode in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="notFilterHkFund != null and notFilterHkFund == 0">
            and t3.IS_HK_PRODUCT!='1'
        </if>
        <if test="notFilterHzFund != null and notFilterHzFund == 0">
            and t3.DISCODE!='HZ000N001'
        </if>
        and t3.ORDERSTATE = '1') hh
    </select>

    <resultMap id="QueryDealOrderVoMap" type="com.howbuy.tms.high.orders.dao.vo.QueryDealOrderVo">
        <id column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="APPOINTMENT_DEAL_NO" jdbcType="VARCHAR" property="appointmentDealNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="M_BUSI_CODE" jdbcType="VARCHAR" property="mBusiCode"/>
        <result column="CUST_NAME" jdbcType="VARCHAR" property="custName"/>
        <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName"/>
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="PAYMENT_TYPE" jdbcType="CHAR" property="paymentType"/>
        <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt"/>
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol"/>
        <result column="APP_DATE" jdbcType="VARCHAR" property="appDate"/>
        <result column="APP_TIME" jdbcType="VARCHAR" property="appTime"/>
        <result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="TX_CHANNEL" jdbcType="CHAR" property="txChannel"/>
        <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt"/>
        <result column="FEE" jdbcType="DECIMAL" property="fee"/>
        <result column="DISCOUNT_RATE" jdbcType="DECIMAL" property="discountRate"/>
        <result column="TX_PMT_FLAG" jdbcType="VARCHAR" property="txPmtFlag"/>
        <result column="PMT_COMP_FLAG" jdbcType="VARCHAR" property="pmtCompFlag"/>
        <result column="PMT_COMPLETE_DTM" jdbcType="TIMESTAMP" property="pmtCompleteDtm"/>
        <result column="CALM_DTM" jdbcType="TIMESTAMP" property="calmDtm"/>
        <result column="FEE_CAL_MODE" jdbcType="VARCHAR" property="feeCalMode"/>
        <result column="SUBMIT_APP_FLAG" jdbcType="VARCHAR" property="submitAppFlag"/>
        <result column="CURRENCY" jdbcType="VARCHAR" property="currency"/>
        <result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="ID_TYPE" jdbcType="CHAR" property="idType"/>
        <result column="ID_NO" jdbcType="VARCHAR" property="idNo"/>
        <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo"/>
        <result column="REDEEM_DIRECTION" jdbcType="VARCHAR" property="redeemDirection"/>
        <result column="NAV" jdbcType="DECIMAL" property="nav"/>
        <result column="ADVANCE_FLAG" jdbcType="VARCHAR" property="advanceFlag"/>
        <result column="FIRST_BUY_FLAG" jdbcType="VARCHAR" property="firstBuyFlag"/>
        <result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm"/>
        <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt"/>
        <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol"/>
        <result column="discount_amt" jdbcType="DECIMAL" property="discountAmt"/>
        <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt"/>
        <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag"/>
        <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo"/>
        <result column="TA_CODE" jdbcType="VARCHAR" property="taCode"/>
        <result column="CONTRACT_NO" jdbcType="VARCHAR" property="contractNo"/>
        <result column="PMT_DEAL_NO" jdbcType="VARCHAR" property="pmtDealNo"/>
        <result column="SUBS_AMT" jdbcType="DECIMAL" property="subsAmt"/>
        <result column="HIGH_FUND_INV_PLAN_FLAG" jdbcType="VARCHAR" property="highFundInvPlanFlag"/>
        <result column="EXTERNAL_DEAL_NO" jdbcType="VARCHAR" property="externalDealNo"/>
        <result column="CUST_RISK_LEVEL" jdbcType="VARCHAR" property="custRiskLevel" />
        <result column="FUND_RISK_LEVEL" jdbcType="VARCHAR" property="fundRiskLevel" />
        <result column="QUALIFICATION_TYPE" jdbcType="VARCHAR" property="qualificationType" />
    </resultMap>

    <select id="queryDealOrderByDealNo" parameterType="map" resultMap="QueryDealOrderVoMap">
        SELECT T1.DEAL_NO,
               T2.APPOINTMENT_DEAL_NO,
               T1.TX_ACCT_NO,
               T3.M_BUSI_CODE,
               T1.CUST_NAME,
               T1.PRODUCT_NAME,
               T1.PRODUCT_CODE,
               T1.PAYMENT_TYPE,
               T1.APP_AMT,
               T1.APP_VOL,
               T1.APP_DATE,
               T1.APP_TIME,
               T1.ORDER_STATUS,
               T1.TX_CHANNEL,
               T1.TA_TRADE_DT,
               T3.FEE,
               T3.CUST_RISK_LEVEL,
               T3.FUND_RISK_LEVEL,
               T3.QUALIFICATION_TYPE,
               T3.DISCOUNT_RATE,
               T3.discount_amt,
               T4.TX_PMT_FLAG,
               T4.PMT_COMP_FLAG,
               T4.PMT_COMPLETE_DTM,
               T4.PMT_DEAL_NO,
               T3.CALM_DTM,
               T3.FEE_CAL_MODE,
               T5.SUBMIT_APP_FLAG,
               '156' AS CURRENCY,
               T1.BANK_ACCT,
               T1.BANK_CODE,
               T1.ID_TYPE,
               T1.ID_NO,
               T1.CP_ACCT_NO,
               T3.REDEEM_DIRECTION,
               T3.NAV,
               T1.ADVANCE_FLAG,
               T3.FIRST_BUY_FLAG,
               T1.APP_DTM,
               T1.EXTERNAL_DEAL_NO,
               T3.ACK_AMT,
               T3.ACK_VOL,
               T3.SUBMIT_TA_DT,
               T3.MERGE_SUBMIT_FLAG,
               T3.MAIN_DEAL_ORDER_NO,
               T3.TA_CODE,
               T3.HIGH_FUND_INV_PLAN_FLAG,
               T3.SUBS_AMT,
               T5.CONTRACT_NO
        FROM DEAL_ORDER T1
                 INNER JOIN (select * from HIGH_DEAL_ORDER_DTL) T3 on T1.DEAL_NO = T3.DEAL_NO
                 LEFT JOIN (select * from DEAL_ORDER_EXTEND where REC_STAT = '0' or REC_STAT is NULL) T2
                           on T1.DEAL_NO = T2.DEAL_NO
                 LEFT JOIN (select * from PAYMENT_ORDER) T4 on T1.DEAL_NO = T4.DEAL_NO
                 LEFT JOIN (select * from simu_fund_check_order) T5 on T1.DEAL_NO = T5.DEAL_NO
        WHERE T1.DEAL_NO = #{dealNo,jdbcType=VARCHAR}
          AND T1.DEAL_TYPE = '2'
    </select>

    <select id="queryByDealNoList" resultMap="BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.orders.dao.mapper.DealOrderPoAutoMapper.Base_Column_List"/>
        from DEAL_ORDER
        where DEAL_NO in
        <foreach collection="dealNoList" item="dealNo" open="(" separator="," close=")">
            #{dealNo}
        </foreach>
    </select>


    <resultMap id="QueryDealOrderMsgVoMap" type="com.howbuy.tms.high.orders.dao.vo.QueryDealOrderMsgVo">
        <id column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="APPOINTMENT_DEAL_NO" jdbcType="VARCHAR" property="appointmentDealNo"/>
        <result column="M_BUSI_CODE" jdbcType="VARCHAR" property="tradeType"/>
        <result column="EXTERNAL_DEAL_NO" jdbcType="VARCHAR" property="externalDealNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
    </resultMap>

    <!-- 查询订单消息 -->
    <select id="selectDealOrderMsgByDealNo" parameterType="map" resultMap="QueryDealOrderMsgVoMap">
        SELECT T.DEAL_NO,
               T.EXTERNAL_DEAL_NO,
               T1.M_BUSI_CODE,
               T2.APPOINTMENT_DEAL_NO,
               T.TX_ACCT_NO
        FROM DEAL_ORDER T
                 INNER JOIN HIGH_DEAL_ORDER_DTL T1 ON T.DEAL_NO = T1.DEAL_NO
                 INNER JOIN (SELECT E.* FROM DEAL_ORDER_EXTEND E WHERE E.REC_STAT = '0' OR E.REC_STAT IS NULL) T2
                            ON T.DEAL_NO = T2.DEAL_NO
        WHERE T.DEAL_NO = #{dealNo,jdbcType=VARCHAR}
    </select>
    <select id="selectRedeemOnWayTradeNum" resultType="java.lang.Integer" parameterType="map">
        select sum(n)
        from (select count(1) n
        from DEAL_ORDER d,high_deal_order_dtl h,DEAL_ORDER_EXTEND e
        where d.deal_no = h.deal_no
        AND d.deal_no = e.deal_no
        AND d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        AND d.order_status = '1'
        AND d.deal_type = '2'
        AND h.m_busi_code = '1124'
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="productCode!=null and productCode!=''">
            and h.fund_code = #{productCode,jdbcType=VARCHAR}
        </if>
        AND (e.rec_stat = '0' or e.rec_stat is null)
        union all
        select count(1) n
        from CM_CUSTTRADE_DIRECT t3
        where t3.txacctno = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t3.discode in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and t3.ORDERSTATE = '1'
        and t3.busicode = '124'
        <if test="productCode!=null and productCode!=''">
            and t3.fundcode = #{productCode,jdbcType=VARCHAR}
        </if>
        ) tt
    </select>

    <select id="selectRedeemOnWayTradeAuthNum" resultType="java.lang.Integer" parameterType="map">
        select sum(n)
        from (select count(1) n
        from DEAL_ORDER d,high_deal_order_dtl h,DEAL_ORDER_EXTEND e
        where d.deal_no = h.deal_no
        AND d.deal_no = e.deal_no
        AND d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        AND d.order_status = '1'
        AND d.deal_type = '2'
        AND h.m_busi_code = '1124'
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="notFilterHzFund != null and notFilterHzFund == 0">
            and d.dis_code!='HZ000N001'
        </if>
        AND (e.rec_stat = '0' or e.rec_stat is null)
        union all
        select count(1) n
        from CM_CUSTTRADE_DIRECT t3
        where t3.txacctno = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and t3.discode in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="notFilterHkFund != null and notFilterHkFund ==0">
            and t3.IS_HK_PRODUCT!='1'
        </if>
        <if test="notFilterHzFund != null and notFilterHzFund ==0">
            and t3.DISCODE!='HZ000N001'
        </if>
        and t3.ORDERSTATE = '1'
        and t3.busicode in ('124','136','19B')
        and t3.RECSTAT='0'
        ) tt
    </select>

    <select id="selectBuyOnwayCpAcctNo" parameterType="map" resultType="string">
        select distinct d.cp_acct_no
        from DEAL_ORDER d
                 inner join high_deal_order_dtl h
                            on d.deal_no = h.deal_no
                 inner join DEAL_ORDER_EXTEND e
                            on d.deal_no = e.deal_no
        where d.order_status = '1'
          AND d.deal_type = '2'
          AND h.m_busi_code in ('1120', '1122')
          and d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
          and h.fund_code = #{fundCode,jdbcType=VARCHAR}
          AND (e.rec_stat = '0' or e.rec_stat is null)
    </select>


    <resultMap id="QueryDealOrderRefundVoMap" type="com.howbuy.tms.high.orders.dao.vo.QueryDealOrderRefundVo">
        <id column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="M_BUSI_CODE" jdbcType="VARCHAR" property="mBusiCode"/>
        <result column="CUST_NAME" jdbcType="VARCHAR" property="custName"/>
        <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName"/>
        <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode"/>
        <result column="PAYMENT_TYPE" jdbcType="CHAR" property="paymentType"/>
        <result column="APP_AMT" jdbcType="DECIMAL" property="appAmt"/>
        <result column="APP_VOL" jdbcType="DECIMAL" property="appVol"/>
        <result column="APP_DATE" jdbcType="VARCHAR" property="appDate"/>
        <result column="APP_TIME" jdbcType="VARCHAR" property="appTime"/>
        <result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="TA_TRADE_DT" jdbcType="VARCHAR" property="taTradeDt"/>
        <result column="FEE" jdbcType="DECIMAL" property="fee"/>
        <result column="DISCOUNT_RATE" jdbcType="DECIMAL" property="discountRate"/>
        <result column="TX_PMT_FLAG" jdbcType="VARCHAR" property="txPmtFlag"/>
        <result column="CALM_DTM" jdbcType="TIMESTAMP" property="calmDtm"/>
        <result column="SUBMIT_APP_FLAG" jdbcType="VARCHAR" property="submitAppFlag"/>
        <result column="BANK_ACCT" jdbcType="VARCHAR" property="bankAcct"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="ID_TYPE" jdbcType="CHAR" property="idType"/>
        <result column="ID_NO" jdbcType="VARCHAR" property="idNo"/>
        <result column="CP_ACCT_NO" jdbcType="VARCHAR" property="cpAcctNo"/>
        <result column="REDEEM_DIRECTION" jdbcType="VARCHAR" property="redeemDirection"/>
        <result column="NAV" jdbcType="DECIMAL" property="nav"/>
        <result column="APP_DTM" jdbcType="TIMESTAMP" property="appDtm"/>
        <result column="ACK_AMT" jdbcType="DECIMAL" property="ackAmt"/>
        <result column="ACK_VOL" jdbcType="DECIMAL" property="ackVol"/>
        <result column="SUBMIT_TA_DT" jdbcType="VARCHAR" property="submitTaDt"/>
        <result column="WITHDRAW_DIRECTION" jdbcType="VARCHAR" property="withdrawDirection"/>
        <result column="REFUND_AMT" jdbcType="DECIMAL" property="refundAmt"/>
        <result column="REFUND_MEMO" jdbcType="VARCHAR" property="refundMemo"/>
        <result column="PRODUCT_CHANNEL" jdbcType="VARCHAR" property="productChannel"/>
    </resultMap>
    <select id="queryDealOrderForRefund" parameterType="map" resultMap="QueryDealOrderRefundVoMap">
        SELECT T1.DEAL_NO,
        T2.APPOINTMENT_DEAL_NO,
        T1.TX_ACCT_NO,
        T3.M_BUSI_CODE,
        T1.CUST_NAME,
        T1.PRODUCT_NAME,
        T1.PRODUCT_CODE,
        T1.PAYMENT_TYPE,
        T1.APP_AMT,
        T1.APP_VOL,
        T1.APP_DATE,
        T1.APP_TIME,
        T1.ORDER_STATUS,
        T1.TX_CHANNEL,
        T1.TA_TRADE_DT,
        T3.FEE,
        T3.DISCOUNT_RATE,
        T4.TX_PMT_FLAG,
        T4.PMT_COMP_FLAG,
        T4.PMT_COMPLETE_DTM,
        T3.CALM_DTM,
        T3.FEE_CAL_MODE,
        T5.SUBMIT_APP_FLAG,
        '156' AS CURRENCY,
        T1.BANK_ACCT,
        T1.BANK_CODE,
        T1.ID_TYPE,
        T1.ID_NO,
        T1.CP_ACCT_NO,
        T3.REDEEM_DIRECTION,
        T3.NAV,
        T1.ADVANCE_FLAG,
        T3.FIRST_BUY_FLAG,
        T1.APP_DTM,
        T3.ACK_AMT,
        T3.ACK_VOL,
        T3.SUBMIT_TA_DT,
        T3.MERGE_SUBMIT_FLAG,
        T3.MAIN_DEAL_ORDER_NO,
        T3.TA_CODE,
        T5.CONTRACT_NO,
        T1.WITHDRAW_DIRECTION,
        T6.REFUND_AMT,
        T6.REFUND_MEMO,
        T1.PRODUCT_CHANNEL,
        T1.DIS_CODE
        FROM DEAL_ORDER T1
        INNER JOIN (select * from HIGH_DEAL_ORDER_DTL) T3 on T1.DEAL_NO = T3.DEAL_NO
        LEFT JOIN (select * from DEAL_ORDER_EXTEND where REC_STAT = '0' or REC_STAT is NULL) T2 on T1.DEAL_NO =
        T2.DEAL_NO
        LEFT JOIN (select * from PAYMENT_ORDER) T4 on T1.DEAL_NO = T4.DEAL_NO
        LEFT JOIN (select * from simu_fund_check_order) T5 on T1.DEAL_NO = T5.DEAL_NO
        LEFT JOIN (select * from DEAL_ORDER_REFUND) T6 on T1.DEAL_NO = T6.DEAL_NO
        WHERE
        T1.TX_ACCT_NO = #{condition.txAcctNo,jdbcType=VARCHAR}
        AND (
        (T3.TX_APP_FLAG IN ('1','2','3') AND T1.PAY_STATUS = '4')
        OR (T3.TX_ACK_FLAG IN ('3','5') AND T3.M_BUSI_CODE IN ('1120','1122'))
        OR (T3.TX_APP_FLAG = '0' AND T3.M_BUSI_CODE IN ('1124') and (T3.TX_ACK_FLAG is null or T3.TX_ACK_FLAG != '5'))
        )
        <if test="condition.productCode != null and condition.productCode != '' ">
            and T1.PRODUCT_CODE = #{condition.productCode,jdbcType=VARCHAR}
        </if>
        <if test="condition.mBusiCode != null and condition.mBusiCode != '' ">
            and T3.M_BUSI_CODE = #{condition.mBusiCode,jdbcType=VARCHAR}
        </if>
        <if test="condition.queryBeginDt != null">
            and t3.submit_ta_dt  <![CDATA[>=]]> #{condition.queryBeginDt,jdbcType=TIMESTAMP}
        </if>
        <if test="condition.queryEndDt != null">
            and t3.submit_ta_dt <![CDATA[<=]]> #{condition.queryEndDt,jdbcType=TIMESTAMP}
        </if>
        <if test="condition.dealNo != null">
            and T1.DEAL_NO = #{condition.dealNo,jdbcType=VARCHAR}
        </if>
    </select>

    <!--查询代销待付款订单：取申请成功但未付款的购买订单笔数-->
    <select id="selectUnpaid" parameterType="map" resultMap="QueryOrderListMap">
        select d.deal_no,
        d.merge_submit_flag,
        d.main_deal_order_no,
        '1' as deal_type/*1-代销*/
    from high_deal_order_dtl d
    inner join deal_order o
    on o.deal_no = d.deal_no
    where d.tx_app_flag = '0'/*0-申请成功*/
      and o.pay_status in ('1', '2')/*1-未付款、2-付款中*/
      and d.m_busi_code in ('1120', '1122')/*1120-认购、1122-申购*/
    <if test="notFilterHzFund!=null and  notFilterHzFund == 0">
      and d.DIS_CODE !='HZ000N001'
    </if>
        <if test="onlyHkProduct!=null and  onlyHkProduct == 1">
            and 1!=1
        </if>
      and o.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
      <if test="disCodeList != null and disCodeList.size() > 0 ">
        and o.dis_code  in
        <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
          #{disCode}
        </foreach>
      </if>
    union all
    select c.appserialno as deal_no,
    '0' as merge_submit_flag,/*0-非合并支付*/
    '' as main_deal_order_no,
    '0' as deal_type/*0-直销*/
    from cm_custtrade_direct c
    where c.busicode in ('120','122','12B','12A')/*120-认购、122-申购,12B-实缴,12A-认缴*/
    and c.prebookstate = '2'/*2-已确认*/
    and c.RECSTAT='0'
    <if test="notFilterHkFund!=null and notFilterHkFund == 0">
      and c.IS_HK_PRODUCT!='1'
    </if>
    <if test="notFilterHzFund!=null and notFilterHzFund == 0">
      and c.DISCODE !='HZ000N001'
     </if>
    <if test="onlyHkProduct!=null and  onlyHkProduct == 1">
      and c.IS_HK_PRODUCT='1'
     </if>
    and c.paystate in ('1','2')/*1-未打款,2-已打款,*/
    and c.orderstate = '1'/*1-申请成功(未确认)*/
    and c.txacctno = #{txAcctNo,jdbcType=VARCHAR}
    and c.tradedt <![CDATA[ >= ]]> '20221101'
    <if test="disCodeList != null and disCodeList.size() > 0 ">
      and c.discode  in
      <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
        #{disCode}
      </foreach>
    </if>


  </select>

    <select id="selectUnconfirmed" parameterType="map" resultMap="QueryOrderListMap">
        /*购买订单：a）代销产品：若【是否进入OP】=是，则取【交易类型】=认购/申购，且【订单状态】=申请成功（order表的order_status对应dtl表的tx_app_flag+tx_ack_flag），且【付款状态】=成功
        的交易记录；*/
        select d.deal_no,
        d.merge_submit_flag,
        d.main_deal_order_no,
        '1' as deal_type/*1-代销*/
        from high_deal_order_dtl d
        inner join deal_order o
        on o.deal_no = d.deal_no
        where d.tx_app_flag = '0'/*0-申请成功*/
        and (d.tx_ack_flag is null or d.tx_ack_flag in ('1', '2'))/*1-未确认、2-确认中*/
        and o.pay_status = '4'/*4-成功*/
        <if test="notFilterHzFund!=null and notFilterHzFund == 0">
            and d.DIS_CODE !='HZ000N001'
        </if>
        <if test="onlyHkProduct!=null and onlyHkProduct == 1">
            and 1!=1
        </if>
        and d.m_busi_code in ('1120', '1122')/*1120-认购、1122-申购*/
        and o.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and o.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        union all
        /*购买订单：b）直销产品：若【是否计入OP】=否，则取CRM中【交易类型】=购买/追加，且【预约状态】=已确认，且【打款状态】=到账确认，且【交易状态】=未确认 的预约记录；*/
        select c.appserialno as deal_no,
        '0' as merge_submit_flag,/*0-非合并支付*/
        '' as main_deal_order_no,
        '0' as deal_type/*0-直销*/
    from cm_custtrade_direct c
    where c.busicode in ('120','122','12B','12A')/*120-认购、122-申购,12B-实缴,12A-认缴*/
      and c.prebookstate = '2'/*2-已确认*/
      and c.RECSTAT='0'
    <if test="notFilterHkFund!=null and notFilterHkFund == 0">
      and c.IS_HK_PRODUCT!='1'
    </if>
    <if test="notFilterHzFund!=null and notFilterHzFund == 0">
      and c.DISCODE !='HZ000N001'
    </if>
    <if test="onlyHkProduct!=null and onlyHkProduct == 1">
      and c.IS_HK_PRODUCT='1'
    </if>
      and c.paystate ='4'/*4-成功（到账确认）*/
      and c.orderstate = '1'/*1-申请成功(未确认)*/
      and c.txacctno = #{txAcctNo,jdbcType=VARCHAR}
      and c.tradedt <![CDATA[ >= ]]> '20221101'
      <if test="disCodeList != null and disCodeList.size() > 0 ">
        and c.discode  in
        <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
          #{disCode}
        </foreach>
      </if>
        union all
        /*购买订单：海外订单,认缴如果不收取手续费,那么待确认订单,需要将待确认而且无需付款的算进去*/
        select c.appserialno as deal_no,
        '0' as merge_submit_flag,/*0-非合并支付*/
        '' as main_deal_order_no,
        '0' as deal_type/*0-直销*/
        from cm_custtrade_direct c
        where c.busicode ='112A' /*112A-认缴*/
        and c.prebookstate = '2'/*2-预约已确认*/
        and c.RECSTAT='0'
        <if test="notFilterHkFund!=null and notFilterHkFund == 0">
            and c.IS_HK_PRODUCT!='1'
        </if>
        <if test="notFilterHzFund!=null and notFilterHzFund == 0">
            and c.DISCODE !='HZ000N001'
        </if>
        <if test="onlyHkProduct!=null and onlyHkProduct == 1">
            and c.IS_HK_PRODUCT='1'
        </if>
        and c.paystate ='0'/*无需付款(0-无需支付)*/
        and c.orderstate = '1'/*1-申请成功(未确认)*/
        and c.txacctno = #{txAcctNo,jdbcType=VARCHAR}
        and c.tradedt <![CDATA[ >= ]]> '20221101'
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and c.discode  in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
    union all
    /*赎回订单：a）代销产品：若【是否进入OP】=是，取【交易类型】=赎回，且【订单状态】=申请成功 的交易记录；*/
    select d.deal_no,
        d.merge_submit_flag,
        d.main_deal_order_no,
        '1' as deal_type/*1-代销*/
        from high_deal_order_dtl d
        where d.m_busi_code = '1124'/*1124-赎回*/
        and d.tx_app_flag = '0'/*0-申请成功*/
        <if test="notFilterHzFund!=null and notFilterHzFund == 0">
            and d.DIS_CODE !='HZ000N001'
        </if>
        <if test="onlyHkProduct!=null and onlyHkProduct == 1">
            and 1!=1
        </if>
        and (d.tx_ack_flag is null or d.tx_ack_flag in ('1', '2'))/*1-未确认、2-确认中*/
        and d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.dis_code in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        union all
        /*赎回订单：b）直销产品：若【是否进入OP】=否，取CRM预约记录中【交易类型】=赎回，【预约状态】=已确认，且【交易状态】= 未确认；*/
        select c.appserialno as deal_no,
        '0' as merge_submit_flag,/*0-非合并支付*/
        '' as main_deal_order_no,
        '0' as deal_type/*0-直销*/
    from cm_custtrade_direct c
    where c.busicode in ('124','136','19B')/*124-赎回*/
      and c.RECSTAT='0'
      and c.prebookstate = '2'/*2-已确认*/
    <if test="notFilterHkFund!=null and notFilterHkFund == 0">
      and c.IS_HK_PRODUCT!='1'
    </if>
    <if test="notFilterHzFund!=null and notFilterHzFund == 0">
      and c.DISCODE !='HZ000N001'
    </if>
    <if test="onlyHkProduct!=null and onlyHkProduct == 1">
        and c.IS_HK_PRODUCT='1'
    </if>
      and c.orderstate = '1'/*1-申请成功(未确认)*/
      and c.txacctno = #{txAcctNo,jdbcType=VARCHAR}
      and c.tradedt <![CDATA[ >= ]]> '20221101'
      <if test="disCodeList != null and disCodeList.size() > 0 ">
        and c.discode  in
        <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
          #{disCode}
        </foreach>
      </if>
  </select>

    <!--查询直销-赎回待回款订单数-->
    <select id="selectDirectRedeemReturn" parameterType="map" resultMap="queryRefundDealResultMap">
        /*若为直销产品，即【是否进入OP】=否，则取“直销交易记录表CM_CUSTTRADE_DIRECT”中【交易类型】=赎回，且【订单状态】=确认成功 的交易记录；*/
        /*若【订单状态】=确认成功，则（【当前时间】-【确认时间】）＜3个工作日 时，计为待回款订单；*/
        select
        c.appserialno as deal_no,
        c.txacctno as TX_ACCT_NO,
        CONCAT('1', c.busicode) AS m_busi_code,
        '0' as MERGE_SUBMIT_FLAG,
        '' as MAIN_DEAL_ORDER_NO,
        ifnull(c.mjjdm,c.fundcode) as fund_code,
        ackamt as refund_amt,
        ack_dt
        from cm_custtrade_direct c
        where c.busicode in('124','136','19B')/*124-赎回*/
        and c.orderstate in ('2','3')/*3-确认成功*/
        and c.txacctno = #{txAcctNo,jdbcType=VARCHAR}
        and c.IS_HK_PRODUCT !='1'
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and c.DISCODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test = "fundCode != null and fundCode != ''">
            and c.fundcode= #{fundCode,jdbcType = VARCHAR}
        </if>
        and c.tradedt > #{tradedt,jdbcType=VARCHAR}
    </select>

    <resultMap id="queryRefundDealResultMap" type="com.howbuy.tms.high.orders.dao.vo.RefundDealOrderVo">
        <result column="DEAL_NO" jdbcType="VARCHAR" property="dealNo"/>
        <result column="TX_ACCT_NO" jdbcType="VARCHAR" property="txAcctNo"/>
        <result column="MERGE_SUBMIT_FLAG" jdbcType="VARCHAR" property="mergeSubmitFlag"/>
        <result column="MAIN_DEAL_ORDER_NO" jdbcType="VARCHAR" property="mainDealOrderNo"/>
        <result column="fund_code" jdbcType="VARCHAR" property="fundCode"/>
        <result column="ta_code" jdbcType="VARCHAR" property="taCode"/>
        <result column="m_busi_code" jdbcType="VARCHAR" property="mBusiCode"/>
        <result column="refund_amt" jdbcType="DECIMAL" property="refundAmt"/>
        <result column="ack_dt" jdbcType="VARCHAR" property="ackDt"/>
        <result column="redeem_direction" jdbcType="VARCHAR" property="redeemDirection"/>
    </resultMap>
    <!--查询代销-购买待退款订单数-->
    <select id="selectConsignmentBuyRefund" parameterType="map" resultMap="queryRefundDealResultMap">
        /*【资金状态】不是已出款的、工作日-撤单日期&lt;=3个工作日的、申请失败、自行撤单和强制撤单的付款成功的、回款方向不为储蓄罐的、认购、申购订单计入【购买待退款订单数】*/
        select
        d.deal_no,
        d.merge_submit_flag,
        d.main_deal_order_no,
        d.fund_code,
        d.m_busi_code,
        d.app_amt as refund_amt,
        d.ack_dt,
        d.redeem_direction
        from high_deal_order_dtl d
        left join deal_order o on d.deal_no = o.deal_no
        left join deal_order_refund r on d.deal_no = r.deal_no
        where (d.pay_out_status is null or d.pay_out_status = '1') /*资金出款状态 0-成功、1-失败*/
        and d.refund_dt >= #{refundDt,jdbcType=VARCHAR}
        and d.tx_app_flag in ('1', '2', '3') /*1-申请失败、2-自行撤销、3-强制取消*/
        and o.pay_status = '4' /*付款状态 4-成功*/
        and d.m_busi_code in ('1120', '1122') /*1120-认购、1122-申购*/
        and ((r.refund_direction is not null and r.refund_direction != '1') /*refund_direction:汇款方向 1-储蓄罐*/
        or (r.refund_direction is null and
        ((o.withdraw_direction is null and o.payment_type != '06') /*支付方式 06-储蓄罐(储蓄罐撤单未选择方向时，默认进储蓄罐)*/
        or (o.withdraw_direction is not null and o.withdraw_direction != '06')))) /*withdraw_direction:撤单实时回储蓄罐 06-储蓄罐*/
        <if test = "fundCode != null and fundCode != ''">
            and d.fund_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        union all
        /*【资金状态】是已出款的、【出款日期】=当前工作日、工作日-撤单日期&lt;=3个工作日的、申请失败、自行撤单和强制撤单的付款成功的、回款方向不为储蓄罐的、认购、申购订单计入【购买待退款订单数】*/
        select
        d.deal_no,
        d.merge_submit_flag,
        d.main_deal_order_no,
        d.fund_code,
        d.m_busi_code,
        d.app_amt as refund_amt,
        d.ack_dt,
        d.redeem_direction
        from high_deal_order_dtl d
        left join deal_order o on d.deal_no = o.deal_no
        left join deal_order_refund r on d.deal_no = r.deal_no
        where d.pay_out_status = 0 /*资金出款状态 0-成功*/
        and d.pay_out_dt = #{payOutDt,jdbcType=VARCHAR}
        and d.refund_dt >= #{refundDt,jdbcType=VARCHAR}
        and d.tx_app_flag in ('1', '2', '3') /*1-申请失败、2-自行撤销、3-强制取消*/
        and o.pay_status = '4' /*付款状态 4-成功*/
        and d.m_busi_code in ('1120', '1122') /*1120-认购、1122-申购*/
        and ((r.refund_direction is not null and r.refund_direction != '1') /*refund_direction:汇款方向 1-储蓄罐*/
        or (r.refund_direction is null and
        ((o.withdraw_direction is null and o.payment_type != '06') /*支付方式 06-储蓄罐(储蓄罐撤单未选择方向时，默认进储蓄罐)*/
        or (o.withdraw_direction is not null and o.withdraw_direction != '06')))) /*withdraw_direction:撤单实时回储蓄罐 06-储蓄罐*/
        and d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test = "fundCode != null and fundCode != ''">
            and d.fund_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        union all
        /*【资金状态】不是已出款的、当前工作日-确认日期&lt;=3个工作日的、且申请金额>确认金额（确认金额不为空）的认购、申购订单计入【购买待退款订单数】*/
        select
        d.deal_no,
        d.merge_submit_flag,
        d.main_deal_order_no,
        d.fund_code,
        d.m_busi_code,
        d.app_amt - d.ack_amt as refund_amt,
        d.ack_dt,
        d.redeem_direction
        from high_deal_order_dtl d
        where (d.pay_out_status is null or d.pay_out_status = '1') /*资金出款状态 0-成功、1-失败*/
        and d.ack_dt >= #{ackDt,jdbcType=VARCHAR}
        and d.app_amt > d.ack_amt
        and d.ack_amt is not null
        and d.m_busi_code in ('1120', '1122') /*1120-认购、1122-申购*/
        and d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test = "fundCode != null and fundCode != ''">
            and d.fund_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        union all
        /*【资金状态】是已出款的、【出款日期】=当前工作日、当前工作日-确认日期&lt;=3个工作日的、且申请金额>确认金额（确认金额不为空）的认购、申购订单计入【购买待退款订单数】*/
        select
        d.deal_no,
        d.merge_submit_flag,
        d.main_deal_order_no,
        d.fund_code,
        d.m_busi_code,
        d.app_amt - d.ack_amt as refund_amt,
        d.ack_dt,
        d.redeem_direction
        from high_deal_order_dtl d
        where d.pay_out_status = 0 /*资金出款状态 0-成功*/
        and d.pay_out_dt = #{payOutDt,jdbcType=VARCHAR}
        and d.ack_dt >= #{ackDt,jdbcType=VARCHAR}
        and d.app_amt > d.ack_amt
        and d.ack_amt is not null
        and d.m_busi_code in ('1120', '1122') /*1120-认购、1122-申购*/
        and d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test = "fundCode != null and fundCode != ''">
            and d.fund_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
    </select>


    <!--查询代销-赎回待回款订单数-未出款-->
    <select id="selectConsignmentRedeemReturnNotPay" parameterType="map" resultMap="queryRefundDealResultMap">
        /*过滤掉异常赎回的交易、【资金状态】不是已出款的、当前工作日-确认日期&lt;=15个工作日的、基金所属的TA不是直销、或者基金所属的TA是直销且基金管理人生成托管指令的、确认状态是终态（确认成功、确认失败、部分确认的）、确认金额不为0、现金分红、赎回、强赎、清盘订单计入【赎回待回款订单数】*/
        /*过滤掉异常赎回的交易、【资金状态】不是已出款的、当前工作日-确认日期&lt;=3个工作日的、基金所属的TA是直销且基金管理人不生成托管指令的、确认状态是终态（确认成功、确认失败、部分确认的）、确认金额不为0、现金分红、赎回、强赎、清盘订单计入【赎回待回款订单数】*/
        select
        d.deal_no,
        d.merge_submit_flag,
        d.fund_code ,
        d.main_deal_order_no,
        d.m_busi_code ,
        d.ta_code,
        d.tx_acct_no,
        d.ack_amt as refund_amt,
        d.ack_dt,
        d.redeem_direction
        from
        high_deal_order_dtl d
        left join deal_order o on
        d.deal_no = o.deal_no
        left join deal_order_refund r on
        d.deal_no = r.deal_no
        where
        (d.pay_out_status is null or d.pay_out_status = '1')/*资金出款状态 0-成功、1-失败*/
        and d.ack_dt >=#{ackDt,jdbcType=VARCHAR}
        and d.ack_amt > 0 /*确认金额不为0*/
        <if test = "fundCode != null and fundCode != ''">
            and d.fund_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and d.m_busi_code in ('1124', '1142','1150','1151','1999','1186','1187')/*赎回/强赎/基金清盘/基金终止/股权回款/到期收益/到期赎回*/
        and d.tx_acct_no =#{txAcctNo,jdbcType=VARCHAR}
        and (d.unusual_trans_type is null or d.unusual_trans_type != '1')
        and d.tx_ack_flag in ('3', '4', '5')
        and (d.CONTINUANCE_FLAG is null or d.CONTINUANCE_FLAG != '1')
        and (d.STAGE_FLAG is null or d.STAGE_FLAG != '1')
        union all
        select
        d.deal_no,
        d.merge_submit_flag,
        d.fund_code ,
        d.main_deal_order_no,
        d.m_busi_code ,
        d.ta_code,
        d.tx_acct_no,
        d.ack_amt as refund_amt,
        d.ack_dt,
        d.redeem_direction
        from
        high_deal_order_dtl d
        left join deal_order o on
        d.deal_no = o.deal_no
        left join deal_order_refund r on
        d.deal_no = r.deal_no
        where
        (d.pay_out_status is null or d.pay_out_status = '1')/*资金出款状态 0-成功、1-失败*/
        and d.ack_dt >=#{ackDt,jdbcType=VARCHAR}
        and d.ack_amt > 0 /*确认金额不为0*/
        and d.fund_div_mode = '1' /*现金分红*/
        <if test = "fundCode != null and fundCode != ''">
            and d.fund_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and d.m_busi_code in ('1143')/*现金分红*/
        and d.tx_acct_no =#{txAcctNo,jdbcType=VARCHAR}
        and (d.unusual_trans_type is null or d.unusual_trans_type != '1')
        and d.tx_ack_flag in ('3', '4', '5')
        and (d.CONTINUANCE_FLAG is null or d.CONTINUANCE_FLAG != '1')
        and (d.STAGE_FLAG is null or d.STAGE_FLAG != '1')
        union all
        select
        l.MAIN_DEAL_NO as deal_no,
        l1.merge_submit_flag,
        l.fund_code ,
        l.ack_dt,
        l1.main_deal_order_no,
        l1.m_busi_code ,
        l1.ta_code,
        l1.tx_acct_no,
        l.ack_amt as refund_amt,
        l1.ack_dt,
        l1.redeem_direction
        from
        HIGH_REDEEM_SPLIT_DTL l
        inner join HIGH_DEAL_ORDER_DTL l1 on l.MAIN_DEAL_NO = l1.DEAL_NO
        where
        (l.pay_out_status is null or l.pay_out_status = '1')/*资金出款状态 0-成功、1-失败*/
        and l.ack_dt >=#{ackDt,jdbcType=VARCHAR}
        and l.ack_amt > 0 /*确认金额不为0*/
        <if test = "fundCode != null and fundCode != ''">
            and l1.fund_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and l1.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and l1.m_busi_code in ('1124', '1142','1150','1151','1999','1186','1187')/*赎回/强赎/基金清盘/基金终止/股权回款/到期收益/到期赎回*/
        and l.tx_acct_no =#{txAcctNo,jdbcType=VARCHAR}
        and (l1.unusual_trans_type is null or l1.unusual_trans_type != '1')
        and l.tx_ack_flag in ('3', '4')
    </select>
    <!--查询代销-赎回待回款订单数-已出款-->
    <select id="selectConsignmentRedeemReturnPay" parameterType="map" resultMap="queryRefundDealResultMap">
        /*过滤掉异常赎回的交易、【资金状态】是已出款的、【出款日期】=当前工作日、当前工作日-确认日期&lt;=15个工作日的、确认状态是终态（确认成功、确认失败、部分确认的）、确认金额不为0、现金分红、赎回、强赎、清盘订单计入【赎回待回款订单数】*/
        select
        d.deal_no,
        d.merge_submit_flag,
        d.fund_code,
        d.main_deal_order_no,
        d.m_busi_code ,
        d.ta_code,
        d.tx_acct_no,
        d.ack_amt as refund_amt,
        d.ack_dt,
        d.redeem_direction
        from high_deal_order_dtl d
        left join deal_order o on d.deal_no = o.deal_no
        left join deal_order_refund r on d.deal_no = r.deal_no
        where d.pay_out_status = 0 /*资金出款状态 0-成功*/
        and d.pay_out_dt = #{payOutDt,jdbcType=VARCHAR}
        and d.ack_dt >= #{ackDt,jdbcType=VARCHAR}
        and d.ack_amt > 0/*确认金额不为0*/
        and d.m_busi_code in ('1143', '1124', '1142' ,'1150') /*1143-分红、1124-赎回、1142-强赎、1150-基金清盘*/
        and d.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test = "fundCode != null and fundCode != ''">
            and d.fund_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and d.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and (d.unusual_trans_type is null or d.unusual_trans_type != '1')
        and d.tx_ack_flag in ('3','4','5')
        and (d.CONTINUANCE_FLAG is null or d.CONTINUANCE_FLAG != '1')
        and (d.STAGE_FLAG is null or d.STAGE_FLAG != '1')
        union all
        select
        l.MAIN_DEAL_NO as deal_no,
        l1.merge_submit_flag,
        l.fund_code as product_code,
        l1.main_deal_order_no,
        l1.m_busi_code,
        l1.ta_code,
        l1.tx_acct_no,
        l.ack_amt as refund_amt,
        l1.ack_dt,
        l1.redeem_direction
        from HIGH_REDEEM_SPLIT_DTL l
        inner join HIGH_DEAL_ORDER_DTL l1 on l.MAIN_DEAL_NO=l1.DEAL_NO
        where l.pay_out_status = 0 /*资金出款状态 0-成功*/
        and l.pay_out_dt = #{payOutDt,jdbcType=VARCHAR}
        and l.ack_dt >= #{ackDt,jdbcType=VARCHAR}
        and l.ack_amt > 0/*确认金额不为0*/
        <if test = "fundCode != null and fundCode != ''">
            and l1.fund_code = #{fundCode,jdbcType = VARCHAR}
        </if>
        <if test="disCodeList != null and disCodeList.size() > 0 ">
            and l1.DIS_CODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        and l1.m_busi_code in ('1143', '1124', '1142' ,'1150') /*1143-分红、1124-赎回、1142-强赎、1150-基金清盘*/
        and l.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        and (l1.unusual_trans_type is null or l1.unusual_trans_type != '1')
        and l.tx_ack_flag in ('3','4')
    </select>

    <select id="selectNotCompleteBuyCountMap" resultType="java.lang.String" parameterType="map">
        select t2.FUND_CODE
        from HIGH_DEAL_ORDER_DTL t2,deal_order t1
        where t2.deal_no = t1.deal_no
        and t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        and t2.FUND_CODE in
        <foreach collection="fundCodes" index="index" item="productCode" open="(" separator="," close=")">
            #{productCode}
        </foreach>
        and t1.deal_type = '2'
        and t1.order_status = '1'
        and t1.pay_status = '4'
        and t2.m_busi_code in ('1120', '1122')
        group by t2.FUND_CODE
    </select>

    <select id="getNotCompleteBuyByTxAcctNoAndDisCode" resultType="java.lang.String" parameterType="map">
        select t2.FUND_CODE
        from HIGH_DEAL_ORDER_DTL t2,
             deal_order t1
        where t2.deal_no = t1.deal_no
          and t2.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
          and t2.dis_code = #{disCode,jdbcType=VARCHAR}
          and t1.deal_type = '2'
          and t1.order_status = '1'
          and t1.pay_status = '4'
          and t2.m_busi_code in ('1120', '1122')
        group by t2.FUND_CODE
    </select>

    <select id="getByExternalDealNo" resultMap="BaseResultMap" parameterType="map">
        select
        <include refid="com.howbuy.tms.high.orders.dao.mapper.DealOrderPoAutoMapper.Base_Column_List"/>
        from DEAL_ORDER
        where EXTERNAL_DEAL_NO = #{externalDealNo,jdbcType=VARCHAR}
        and dis_code = #{disCode,jdbcType=VARCHAR}
    </select>

    <select id="countUnPayOrPayingOrder" resultType="int">
        select count(1) from (
        select deal_no from deal_order
        where pay_status in('1','2')
        and deal_type = '2'
        and order_status = '1'
        and tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCode != null and disCode != ''">
            and dis_code = #{disCode,jdbcType=VARCHAR}
        </if>
        <if test="cpAcctNos!=null and cpAcctNos.size()>0" >
            and cp_acct_no in
            <foreach collection="cpAcctNos" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        union all
        select t.deal_no from (
        select deal_no from deal_order where ta_trade_dt &gt;= #{taTradeDt,jdbcType=VARCHAR}
        and deal_type = '2'
        and advance_flag = '2'
        and order_status = '6'
        and payment_type = '01'
        and pay_status = '2'
        and tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCode != null">
            and dis_code = #{disCode,jdbcType=VARCHAR}
        </if>
        <if test="cpAcctNos!=null and cpAcctNos.size()>0" >
            and cp_acct_no in
            <foreach collection="cpAcctNos" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) t,deal_order_extend p
        where t.deal_no = p.deal_no and p.rec_stat = '1'
        ) tt
    </select>

    <select id="isUnCheckDealOrder" resultType="int">
        select count(1) from SUBMIT_UNCHECK_ORDER
        where PRODUCT_CLASS = '3'
        and CHECK_FLAG = '0'
        and tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
        <if test="disCode != null">
            and dis_code = #{disCode,jdbcType=VARCHAR}
        </if>
        <if test="cpAcctNos!=null and cpAcctNos.size()>0" >
            and cp_acct_no in
            <foreach collection="cpAcctNos" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByDealNo" resultMap="BaseResultMap">
        select
        <include refid="com.howbuy.tms.high.orders.dao.mapper.DealOrderPoAutoMapper.Base_Column_List"/>
        from deal_order
        where deal_no = #{dealNo,jdbcType=VARCHAR}
    </select>



    <update id="updateByDealNoSelective">
        update deal_order
        <set>
            <if test="dealNo != null">
                deal_no = #{dealNo,jdbcType=VARCHAR},
            </if>
            <if test="txAcctNo != null">
                tx_acct_no = #{txAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="disCode != null">
                dis_code = #{disCode,jdbcType=VARCHAR},
            </if>
            <if test="disTxAcctNo != null">
                dis_tx_acct_no = #{disTxAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="outletCode != null">
                outlet_code = #{outletCode,jdbcType=VARCHAR},
            </if>
            <if test="cpAcctNo != null">
                cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="subTxAcctNo != null">
                sub_tx_acct_no = #{subTxAcctNo,jdbcType=VARCHAR},
            </if>
            <if test="bankAcct != null">
                bank_acct = #{bankAcct,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                bank_code = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="custName != null">
                cust_name = #{custName,jdbcType=VARCHAR},
            </if>
            <if test="idType != null">
                id_type = #{idType,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null">
                id_no = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="txCode != null">
                tx_code = #{txCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                product_code = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="protocolName != null">
                protocol_name = #{protocolName,jdbcType=VARCHAR},
            </if>
            <if test="protocolNo != null">
                protocol_no = #{protocolNo,jdbcType=VARCHAR},
            </if>
            <if test="paymentType != null">
                payment_type = #{paymentType,jdbcType=CHAR},
            </if>
            <if test="appAmt != null">
                app_amt = #{appAmt,jdbcType=DECIMAL},
            </if>
            <if test="appVol != null">
                app_vol = #{appVol,jdbcType=DECIMAL},
            </if>
            <if test="appRatio != null">
                app_ratio = #{appRatio,jdbcType=DECIMAL},
            </if>
            <if test="appDate != null">
                app_date = #{appDate,jdbcType=VARCHAR},
            </if>
            <if test="appTime != null">
                app_time = #{appTime,jdbcType=VARCHAR},
            </if>
            <if test="appDtm != null">
                app_dtm = #{appDtm,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDtm != null">
                update_dtm = #{updateDtm,jdbcType=TIMESTAMP},
            </if>
            <if test="payStatus != null">
                pay_status = #{payStatus,jdbcType=CHAR},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="txChannel != null">
                tx_channel = #{txChannel,jdbcType=CHAR},
            </if>
            <if test="invstType != null">
                invst_type = #{invstType,jdbcType=CHAR},
            </if>
            <if test="ipAddress != null">
                ip_address = #{ipAddress,jdbcType=VARCHAR},
            </if>
            <if test="dataTrack != null">
                data_track = #{dataTrack,jdbcType=VARCHAR},
            </if>
            <if test="taTradeDt != null">
                ta_trade_dt = #{taTradeDt,jdbcType=VARCHAR},
            </if>
            <if test="protocolType != null">
                protocol_type = #{protocolType,jdbcType=VARCHAR},
            </if>
            <if test="externalDealNo != null">
                external_deal_no = #{externalDealNo,jdbcType=VARCHAR},
            </if>
            <if test="advanceFlag != null">
                advance_flag = #{advanceFlag,jdbcType=VARCHAR},
            </if>
            <if test="dealType != null">
                deal_type = #{dealType,jdbcType=VARCHAR},
            </if>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="productRiskLevel != null">
                product_risk_level = #{productRiskLevel,jdbcType=VARCHAR},
            </if>
            <if test="zBusiCode != null">
                z_busi_code = #{zBusiCode,jdbcType=VARCHAR},
            </if>
            <if test="productClass != null">
                product_class = #{productClass,jdbcType=VARCHAR},
            </if>
            <if test="productChannel != null">
                product_channel = #{productChannel,jdbcType=VARCHAR},
            </if>
            <if test="createDtm != null">
                create_dtm = #{createDtm,jdbcType=TIMESTAMP},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=VARCHAR},
            </if>
            <if test="productSubType != null">
                product_sub_type = #{productSubType,jdbcType=VARCHAR},
            </if>
            <if test="orderSource != null">
                order_source = #{orderSource,jdbcType=CHAR},
            </if>
            <if test="partnerCode != null">
                partner_code = #{partnerCode,jdbcType=VARCHAR},
            </if>
            <if test="adviserDealNo != null">
                adviser_deal_no = #{adviserDealNo,jdbcType=VARCHAR},
            </if>
            <if test="oriDealNo != null">
                ori_deal_no = #{oriDealNo,jdbcType=VARCHAR},
            </if>
            <if test="qualificationType != null">
                qualification_type = #{qualificationType,jdbcType=VARCHAR},
            </if>
            <if test="withdrawDirection != null">
                withdraw_direction = #{withdrawDirection,jdbcType=VARCHAR},
            </if>
            <if test="bankAcctCipher != null">
                bank_acct_cipher = #{bankAcctCipher,jdbcType=VARCHAR},
            </if>
            <if test="couponId != null">
                coupon_id = #{couponId,jdbcType=VARCHAR},
            </if>
            <if test="followDealNo != null">
                follow_deal_no = #{followDealNo,jdbcType=VARCHAR},
            </if>
            <if test="subOutletCode != null">
                sub_outlet_code = #{subOutletCode,jdbcType=VARCHAR},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where deal_no = #{dealNo,jdbcType=VARCHAR}
    </update>
</mapper>