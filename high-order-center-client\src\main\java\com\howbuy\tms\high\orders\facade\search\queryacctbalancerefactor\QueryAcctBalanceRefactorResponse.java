package com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor;

import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.BalanceInfoBean;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancerefactor.bean.UnconfirmedProductBean;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 查询客户持仓响应（重构版）
 * @author: hongdong.xie
 * @date: 2025/8/26 14:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryAcctBalanceRefactorResponse extends OrderSearchBaseResponse {

    private static final long serialVersionUID = 1L;

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 分销机构代码
     */
    private String disCode;

    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;

    /**
     * 总市值（人民币）
     */
    private BigDecimal totalMarketValue;

    /**
     * 总在途金额（人民币）
     */
    private BigDecimal totalUnconfirmedAmt;

    /**
     * 总在途产品数量
     */
    private Integer totalUnconfirmedNum;

    /**
     * 赎回在途产品数量
     */
    private Integer redeemUnconfirmedNum;

    /**
     * 当前总收益（人民币）
     */
    private BigDecimal totalCurrentAsset;

    /**
     * 总收益计算状态
     * 0-计算中，1-计算成功
     */
    private String totalIncomCalStat;

    /**
     * 总回款金额（人民币）
     */
    private BigDecimal totalCashCollection;

    /**
     * 是否持有好臻产品
     * 0-没有，1-有
     */
    private String hasHZProduct = YesOrNoEnum.NO.getCode();

    /**
     * 是否持有好买香港产品
     * 0-没有，1-有
     */
    private String hasHKProduct = YesOrNoEnum.NO.getCode();

    /**
     * 持仓明细列表
     */
    private List<BalanceInfoBean> balanceList = new ArrayList<>();

    /**
     * 在途产品列表
     */
    private List<UnconfirmedProductBean> unconfirmedProducts = new ArrayList<>();
}
