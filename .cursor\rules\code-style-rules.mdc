---
description: Code Style and Structure 编码规范和结构
globs: *.java
alwaysApply: false
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
代码风格:
  命名约定:
    - 变量: camelCase  # 变量名使用小驼峰
    - 方法: camelCase  # 方法名使用小驼峰
    - 类: PascalCase    # 类名使用大驼峰
    - 常量: UPPERCASE_WITH_UNDERSCORES  # 常量使用全大写+下划线分隔
  缩进: 4_spaces  # 使用 4 个空格进行缩进
  每行最大长度: 120  # 每行代码不得超过 120 个字符
  代码规范:
    - 使用阿里巴巴代码规范
    - 使用lombok注解
    - 使用dubbo注解

  导包顺序:
    - 静态导入优先: true
    - 包顺序:
        - java
        - javax
        - org
        - com
  注释要求:
    - Controller层、Service层、repository层和关键业务逻辑必须添加 Javadoc 注释
    - @RequestMapping/@GetMapping 等注解需说明 API 的用途及注意事项
    - 类和方法注释需说明其功能和使用场景
    - 注释需清晰、简洁、准确，避免冗余和模糊
    - 注释需使用中文
    - Dubbo接口注释需说明其功能和使用场景
    - 方法注释规范
      - @description
      - @param
      - @return
      - <AUTHOR> @date 